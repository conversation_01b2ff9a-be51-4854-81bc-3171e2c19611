const { Watchlist, Address, MasterData, WatchlistHistoryView , WatchlistDocument } = require('../models');
const { paginate } = require('../models/plugins/paginate.plugin');
const { sendSuccess, sendError, catchAsync } = require('../helpers/api.helper');
const { status: httpStatus } = require('http-status');
const {formatIdentity} = require('../helpers/global.helper');
const { Op } = require('sequelize');

// Create a new watchlist entry
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const {
    first_name,
    middle_name,
    last_name,
    suffix,
    date_of_birth,
    email,
    phone,
    host,
    address,
    status,
    image,
    risk_level,
    updated_by,
    expiry_date,
  } = req.body;

  const watchlistEntry = await Watchlist.create(
    {
      first_name,
      middle_name,
      last_name,
      suffix,
      date_of_birth,
      email,
      phone,
      host,
      address,
      status,
      image,
      risk_level,
      updated_by,
      expiry_date,
    },
    { transaction }
  );

  sendSuccess(res, 'Watchlist entry created successfully', httpStatus.CREATED, watchlistEntry);
});

exports.details = async (req, res, next) => {

  const { watchlist_id } = req.query;

  if (!watchlist_id) {
    return sendError(res, 'watchlist_id is required', httpStatus.BAD_REQUEST);
  }

  const queryOptions = {
    where: {
      watchlist_id,
    },
    attributes: [
      'first_name',
      'last_name',
      'image',
      'address',
      'status',
      'phone',
      'date_of_birth',
      'gender',
      'hair_color',
      'created_by',
      'expiry_date'
    ],

  };

  const result = await Watchlist.findOne(queryOptions);
  if (!result) {
    return sendError(res, 'No watchlist found', httpStatus.NOT_FOUND);
  }

  // Format dates to show only date without time
  const formattedResult = result.get({ plain: true });
  if (formattedResult.date_of_birth) {
    formattedResult.date_of_birth = new Date(formattedResult.date_of_birth).toISOString().split('T')[0];
  }
  if (formattedResult.expiry_date) {
    formattedResult.expiry_date = new Date(formattedResult.expiry_date).toISOString().split('T')[0];
  }

  const identityEid = await formatIdentity(formattedResult.created_by);
  if (identityEid) {
    formattedResult.created_by = identityEid; // Add the formatted identity to the result
  } else {
    formattedResult.formatted_identity = 'Identity not found'; // Handle case where identity is not found
  }

  sendSuccess(res, 'Watchlist details retrieved successfully', httpStatus.OK, formattedResult);
};

// Retrieve a watchlist entry by ID
exports.getWatchlistById = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const watchlistEntry = await Watchlist.findByPk(id);

  if (!watchlistEntry) {
    return sendError(res, 'Watchlist entry not found', httpStatus.NOT_FOUND);
  }

  // Format dates to show only date without time
  const formattedEntry = watchlistEntry.get({ plain: true });
  if (formattedEntry.date_of_birth) {
    formattedEntry.date_of_birth = new Date(formattedEntry.date_of_birth).toISOString().split('T')[0];
  }
  if (formattedEntry.expiry_date) {
    formattedEntry.expiry_date = new Date(formattedEntry.expiry_date).toISOString().split('T')[0];
  }

  sendSuccess(res, 'Watchlist entry retrieved successfully', httpStatus.OK, formattedEntry);
});

// Retrieve all watchlist entries with pagination, sorting, and ordering
exports.getWatchlists = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, sortBy = "created_at", sortOrder = "DESC", search } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
  };

  if (search) {
   queryOptions.where = {
  [Op.or]: [
    { first_name: { [Op.iLike]: `%${search}%` } },
    { last_name: { [Op.iLike]: `%${search}%` } },
    { email: { [Op.iLike]: `%${search}%` } },
    { phone: { [Op.iLike]: `%${search}%` } },
    { host: { [Op.iLike]: `%${search}%` } },
  ],
};
  }

  const result = await paginate(Watchlist, queryOptions, paginationOptions);

  // Format dates for all entries
  if (result.data && result.data.length > 0) {
    result.data = result.data.map(entry => {
      const formattedEntry = entry.get ? entry.get({ plain: true }) : entry;
      if (formattedEntry.date_of_birth) {
        formattedEntry.date_of_birth = new Date(formattedEntry.date_of_birth).toISOString().split('T')[0];
      }
      if (formattedEntry.expiry_date) {
        formattedEntry.expiry_date = new Date(formattedEntry.expiry_date).toISOString().split('T')[0];
      }
      return formattedEntry;
    });
  }

  sendSuccess(res, "Watchlist entries retrieved successfully", httpStatus.OK, result);
});

// Update a watchlist entry by ID
exports.updateWatchlist = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { id } = req.params;
  const { address, ...watchlistData } = req.body;

  const watchlistEntry = await Watchlist.findByPk(id);
  if (!watchlistEntry) {
    return sendError(res, 'Watchlist entry not found', httpStatus.NOT_FOUND);
  }

  await watchlistEntry.update({ ...watchlistData, address }, { transaction });

  sendSuccess(res, 'Watchlist entry updated successfully', httpStatus.OK, watchlistEntry);
});

// Delete a watchlist entry by ID
exports.deleteWatchlist = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const watchlistEntry = await Watchlist.findByPk(id);
  if (!watchlistEntry) {
    return sendError(res, 'Watchlist entry not found', httpStatus.NOT_FOUND);
  }
  await watchlistEntry.destroy();
  sendSuccess(res, 'Watchlist entry deleted successfully', httpStatus.NO_CONTENT);
});

exports.getWatchlistHistory = catchAsync(async (req, res, next) => {
  const { watchlist_id } = req.params;

  const result = await WatchlistHistoryView.findAll({
    where: { watchlist_id },
    order: [['effective_date', 'DESC']],
  });

  if (!result || result.length === 0) {
    return sendError(res, 'No history found for the given watchlist', httpStatus.NOT_FOUND);
  }

  // Helper function to convert event_type to descriptive name
  const getEventTypeName = (eventType) => {
    switch (parseInt(eventType)) {
      case 0:
        return 'Create';
      case 1:
        return 'Update';
      case 2:
        return 'Delete';
      default:
        return 'Unknown';
    }
  };

  const formattedResults = await Promise.all(
    result.map(async (record) => {
      // Convert updated_by to the correct data type if necessary (e.g., string to number)
      const identityId = record.modified_by;

      // Format the identity
      const formattedIdentity = await formatIdentity(identityId);
      const recordData = record.get({ plain: true });

      return {
        ...recordData,
        updated_by: formattedIdentity || `Identity not found (ID: ${identityId})`,
        event_type: getEventTypeName(recordData.event_type), // Convert numeric value to descriptive name
      };
    })
  );

  sendSuccess(res, 'Watchlist history retrieved successfully', httpStatus.OK, formattedResults);
});

exports.getAllWatchlistHistory = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, sortBy = "effective_date", sortOrder = "DESC" } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    raw: false, // Ensure we get Sequelize instances so we can call .get({ plain: true })
  };

  const histories = await paginate(WatchlistHistoryView, queryOptions, paginationOptions);

  if (!histories.data || histories.data.length === 0) {
    return sendError(res, 'No history found for the given patient', httpStatus.NOT_FOUND);
  }

  // Helper function to convert event_type to descriptive name
  const getEventTypeName = (eventType) => {
    switch (parseInt(eventType)) {
      case 0:
        return 'Create';
      case 1:
        return 'Update';
      case 2:
        return 'Delete';
      default:
        return 'Unknown';
    }
  };

  // Format updated_by for each history entry
  const formattedHistories = await Promise.all(
    histories.data.map(async (history) => {
      // Convert Sequelize instance to plain object
      const historyData = history.get ? history.get({ plain: true }) : history;
      const identityId = historyData.modified_by;
      const formattedIdentity = await formatIdentity(identityId);
      return {
        ...historyData,
        updated_by: formattedIdentity || `Identity not found (ID: ${identityId})`,
        event_type: getEventTypeName(historyData.event_type), // Convert numeric value to descriptive name
      };
    })
  );

  sendSuccess(res, 'Watchlist history retrieved successfully', httpStatus.OK, {
    ...histories,
    data: formattedHistories,
  });
});

exports.image = catchAsync(async (req, res) => {
  const { watchlist_id } = req.params;
  const watchlist = await Watchlist.findByPk(watchlist_id);
  const { image } = req.body; // or req.file if using multer
  watchlist.image = image;
  await watchlist.save();
  return sendSuccess(res, "watchlist image updated successfully", httpStatus.OK, watchlist);
});

exports.document = catchAsync(async (req, res) => {
  const { watchlist_id } = req.params;
  const { name } = req.body;
  const image = req.body.image;
  const newDocument = await WatchlistDocument.create({
    watchlist_id, // Foreign key from the URL parameter
    name,         // Name from the request body
    documents: image, // Assuming 'image' is the field for documents
  });
  return sendSuccess(res, "Watchlist document created successfully", httpStatus.CREATED, newDocument);
});
exports.getdocument = catchAsync(async (req, res) => {
  const { watchlist_id } = req.params;
  const document = await WatchlistDocument.findAll({
    where: { watchlist_id },
  });
  return sendSuccess(res, "Watchlist document retrieved successfully", httpStatus.OK, document);
});
exports.deletedocument = catchAsync(async (req, res) => {
  const { watchlist_document_id } = req.params;
  const existingDocument = await WatchlistDocument.findOne({
    where: { watchlist_document_id },
  })
  // Delete the document
  const deletedCount = await WatchlistDocument.destroy({
    where: { watchlist_document_id },
  });

  return sendSuccess(res, "Watchlist document deleted successfully", httpStatus.OK, {
    watchlist_document_id,
    deleted_count: deletedCount
  });
});



