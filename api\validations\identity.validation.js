const Joi = require("joi");
const { getModelAttributes } = require("../helpers/global.helper");
const {Identity , IdentityAccess , Card}= require("../models");
const { identity } = require("lodash");

const identityAttributes = getModelAttributes(Identity);
const cardAttributes = getModelAttributes(Card);
const identityAccessAttribute = getModelAttributes(IdentityAccess)

module.exports = {
  createIdentity: {
    body: Joi.object().keys({
      first_name: Joi.string().required(),
      middle_name: Joi.string().optional().allow(""),
      last_name: Joi.string().required(),
      suffix: Joi.string().optional().allow(""),
      email: Joi.string().email().optional().allow(""),
      identity_type: Joi.number().integer().required(),
      national_id: Joi.string().optional().allow(""),
      mobile: Joi.string().optional().allow(""),
      eid: Joi.string().alphanum().optional().allow(""),
      start_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      end_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      status: Joi.number().integer().required(),
      suspension: Joi.boolean().optional().allow(""),
      suspension_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      reason: Joi.string().optional().allow(""),
      image: Joi.string().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },
  getIdentityById: {
    params: Joi.object().keys({
      identity_id: Joi.string().uuid().required(),
    }),
  },
  getIdentity: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...identityAttributes).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      name: Joi.string().optional(),
      eid: Joi.string().uuid().optional(),
      type: Joi.string().optional(),
      job_title:Joi.string().optional(),
    }),
  },
  updateIdentity: {
    params: Joi.object().keys({
      identity_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      first_name: Joi.string().optional().allow(""),
      last_name: Joi.string().optional().allow(""),
      middle_name: Joi.string().optional().allow(""),
      suffix: Joi.string().optional().allow(""),
      email: Joi.string().email().optional().allow(""),
      identity_type: Joi.number().integer().optional(),
      national_id: Joi.string().optional().allow(""),
      mobile: Joi.string().optional().allow(""),
      start_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      end_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      status: Joi.number().integer().optional(),
      suspension: Joi.boolean().optional().allow(""),
      suspension_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      reason: Joi.string().optional().allow(""),
      image: Joi.string().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },
  deleteIdentity: {
    params: Joi.object().keys({
      identity_id: Joi.string().uuid().required(),
    }),
  },

  createCard: {
    body: Joi.object().keys({
      card_number: Joi.string().required(),
      card_format: Joi.number().integer().required(),
      facility_code: Joi.number().integer().optional().allow(null),
      pin: Joi.number().integer().optional().allow(null),
      template: Joi.number().integer().required(),
      active_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      deactive_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      reason: Joi.string().optional().allow(""),
      status: Joi.number().integer().required(),
      identity_id: Joi.string().uuid().required(),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },
  getCardById: {
    params: Joi.object().keys({
      card_id: Joi.string().uuid().required(),
    }),
  },
  getCards: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...cardAttributes).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      card_number: Joi.string().optional(),
      identity_id: Joi.string().uuid().optional(),
      // eid: Joi.string().uuid().optional(),
      card_format: Joi.string().optional()
    }),
  },
  updateCard: {
    params: Joi.object().keys({
      card_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      card_number: Joi.string().optional().allow(""),
      card_format: Joi.number().integer().optional(),
      facility_code: Joi.number().integer().optional().allow(null),
      pin: Joi.number().integer().optional().allow(null),
      template: Joi.number().integer().optional(),
      active_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      deactive_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      reason: Joi.string().optional().allow(""),
      status: Joi.number().integer().optional(),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },
  deleteCard: {
    params: Joi.object().keys({
      card_id: Joi.string().uuid().required(),
    }),
  },

  getCardList: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid("card_number", "template", "created_by", "deactive_date", "status").optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      card_number: Joi.string().optional(),
      card_format: Joi.string().optional(),
      search: Joi.string().optional(),
    }),
  },

  createIdentityAccess: {
    body: Joi.object().keys({
      access_level_id: Joi.string().uuid().required(),
      card_id: Joi.string().uuid().required(),
      start_date: Joi.date().required(),
      end_date: Joi.date().required(),
      status: Joi.number().integer().required(),
      identity_id: Joi.string().uuid().required(),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },
  getIdentityAccess: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...identityAccessAttribute).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      area_name: Joi.string().optional(),
      eid: Joi.string().uuid().optional(),
      pacs_area_name: Joi.string().optional(),
      system: Joi.string().optional(),
      search: Joi.string().optional(),
    }),
  },
  getIdentityAccessById: {
    params: Joi.object().keys({
      identity_access_id: Joi.string().uuid().required(),
    }),
  },
  updateIdentityAccess: {
    params: Joi.object().keys({
      identity_access_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      access_level_id: Joi.string().uuid().optional().allow(""),
      card_id: Joi.string().uuid().optional().allow(""),
      start_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      end_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      status: Joi.number().integer().optional(),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },
  deleteIdentityAccess: {
    params: Joi.object().keys({
      identity_access_id: Joi.string().uuid().required(),
    }),
  },

  getIdentityHub: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      name: Joi.string().optional(),
      eid: Joi.string().uuid().optional(),
      type: Joi.string().optional(),
      search: Joi.string().optional(),
    }),
  },

  getIdentityAccessList: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      area_name: Joi.string().optional(),
      eid: Joi.string().uuid().optional(),
      pacs_area_name: Joi.string().optional(),
      system: Joi.string().optional(),
    }),
  },

  getIdentityAccessDetails: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      status: Joi.number().integer().optional(),
      search: Joi.string().allow("").optional(),
    }),
  },

  getOrganization: {
    query: Joi.object().keys({
      identity_id: Joi.string().uuid().optional(),
    }),
  },



  updateOrganization: {
    body: Joi.object().keys({
      identity_id: Joi.string().uuid().required(),
      first_name: Joi.string().optional(),
      last_name: Joi.string().optional(),
      eid: Joi.string().optional(),
      company: Joi.string().optional(),
      company_code: Joi.string().optional(),
      organization: Joi.string().optional(),
      job_title: Joi.string().optional(),
      job_code: Joi.string().optional(),
      manager: Joi.string().optional(),
      status: Joi.number().integer().optional(),
    }),
  },

  getAccessLevelName: {
    query: Joi.object().keys({
      search: Joi.string().required(),
    }),
  },

  updateFacility: {
    body: Joi.object().keys({
      facility_id: Joi.string().uuid().required(),
      identity_id: Joi.string().uuid().required(),
    }),
  },

  getIdentityAccessByIdentity: {
    params: Joi.object().keys({
      identity_id: Joi.string().uuid().required(),
    }),
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      search: Joi.string().optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
    }),
  },
};
