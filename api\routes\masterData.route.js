const express = require("express");
const validate = require("../middlewares/validate");
const {
  MasterDataValidation,
  BuildingValidation,
  FloorValidation,
  RoomValidation,
} = require("../validations");
const {
  <PERSON><PERSON><PERSON><PERSON>ontroller,
  BuildingController,
  FloorController,
  RoomController,
  FacilityController
} = require("../controllers");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: MasterData
 *   description: Master data retrieval and management
 */

/**
 * @swagger
 * /master-data:
 *   get:
 *     summary: Retrieve master data key values by groups
 *     tags: [MasterData]
 *     parameters:
 *       - in: query
 *         name: groups
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             enum: [visit_type, identity_type, facility_status, facility_type, building_status, building_type, building_occupancy_type, floor_status, floor_occupancy_type, room_status, watchlist_status, watchlist_reason, risk_level, appointment_type, appointment_status, patient_gender, patient_confidentiality_code, patient_marital_status, patient_preferred_language, patient_identifier_identifier_type, patient_identifier_assigning_authority, patient_guest_guest_type, appointment_guest_status, patient_guest_relation_type, patient_guest_relationship_status, event_actions_status, trace_actions_status, guest_screening, override_screening, screening_match_type, card_format, card_status, card_template, identity_status, access_level_status, access_level_type, identity_access_status, nda_agreement_status, signer_role, signature_method, document_type, document_status, training_status, delegate_status, course_type, recurrence, guest_status, visit_category, repeat_visit, check_in_instruction, remind_me, visit_status]
 *         required: true
 *         description: Array of group IDs to filter master data records. If a single group is provided, it will be converted into an array.
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Master data key values retrieved successfully.
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - key: "exampleKey"
 *                   value: "exampleValue"
 */
router.get(
  "/",
  auth("view_master_data"),
  validate(MasterDataValidation.getMasterData),
  MasterDataController.getMasterData
);

/**
 * @swagger
 * /master-data/buildings/{facilityId}:
 *   get:
 *     summary: Get all buildings for a facility (without pagination)
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID for which to fetch buildings.
 *     responses:
 *       200:
 *         description: List of buildings with only building_id and name.
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - building_id: "64b8f0e2d123e4567890abcd"
 *                   name: "Main Building"
 */
router.get(
  "/buildings/:facilityId",
  auth("fetch_buildings"),
  validate(BuildingValidation.facility),
  BuildingController.fetch
);

/**
 * @swagger
 * /master-data/floors/{buildingId}:
 *   get:
 *     summary: Get all floors for a building (without pagination)
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: buildingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The building ID for which to fetch floors.
 *     responses:
 *       200:
 *         description: List of floors with limited fields (floor_id and name).
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - floor_id: "64b8f0e2d123e4567890abcd"
 *                   floor_number: 3
 */
router.get(
  "/floors/:buildingId",
  auth("fetch_floors"),
  validate(FloorValidation.building),
  FloorController.fetch
);

/**
 * @swagger
 * /master-data/rooms/{floorId}:
 *   get:
 *     summary: Get all rooms for a floor (without pagination)
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: floorId
 *         required: true
 *         schema:
 *           type: string
 *         description: The floor ID for which to fetch rooms.
 *     responses:
 *       200:
 *         description: List of rooms with limited fields (room id and name).
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - room_id: "64b8f0e2d123e4567890abcd"
 *                   room_number: 212
 */
router.get(
  "/rooms/:floorId",
  auth("fetch_rooms"),
  validate(RoomValidation.floor),
  RoomController.fetch
);

/**
 * @swagger
 * /master-data/media/{model}:
 *   get:
 *     summary: Get media for any model by model name and media reference UUID.
 *     tags: [MasterData]
 *     parameters:
 *       - in: path
 *         name: model
 *         required: true
 *         schema:
 *           type: string
 *         description: The model name from which to fetch the media (e.g. "Facility", "Watchlist").
 *         example: "Watchlist"
 *       - in: query
 *         name: key
 *         schema:
 *           type: string
 *           default: "image"
 *         description: The media key (defaults to "image" if not provided).
 *         example: "documents"
 *       - in: query
 *         name: value
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The media reference UUID.
 *         example: "5a2879a9-b03b-4c90-b174-b6f653d6d343"
 *       - in: query
 *         name: thumbnail_only
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to return thumbnail version (for images only). Falls back to full image if thumbnail doesn't exist.
 *         example: true
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Media record retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Watchlist image retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     key:
 *                       type: string
 *                       example: "documents"
 *                     value:
 *                       type: string
 *                       description: Base64 encoded media data (full image or thumbnail)
 *                       example: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
 *       404:
 *         description: Media not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Watchlist image not available"
 */
router.get(
  "/media/:model",
  auth("view_master_data"),
  validate(MasterDataValidation.media),
  MasterDataController.getMedia
);

/**
 * @swagger
 * /master-data/countries:
 *   get:
 *     summary: Get all countries
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of countries
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - country_id: "64b8f0e2d123e4567890abcd"
 *                   name: "United States"
 */
router.get(
  "/countries",
  auth("view_master_data"),
  MasterDataController.getAllCountries
);

/**
 * @swagger
 * /master-data/states:
 *   get:
 *     summary: Get all states
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of states
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - state_id: "64b8f0e2d123e4567890abcd"
 *                   name: "California"
 *                   country: { country_id: "64b8f0e2d123e4567890abcd", name: "United States" }
 */
router.get(
  "/states",
  auth("view_master_data"),
  MasterDataController.getAllStates
);

/**
 * @swagger
 * /master-data/states/{countryId}:
 *   get:
 *     summary: Get all states for a specific country
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: countryId
 *         required: true
 *         schema:
 *           type: string
 *         description: The country ID for which to fetch states.
 *     responses:
 *       200:
 *         description: List of states for the given country.
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - state_id: "64b8f0e2d123e4567890abcd"
 *                   name: "California"
 */
router.get(
  "/states/:countryId",
  auth("view_master_data"),
  MasterDataController.getStatesByCountry
);

/**
 * @swagger
 * /master-data/timezones:
 *   get:
 *     summary: Get all timezones
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of timezones
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - time_id: "64b8f0e2d123e4567890abcd"
 *                   code: "PST"
 *                   country: { country_id: "64b8f0e2d123e4567890abcd", name: "United States" }
 */
router.get(
  "/timezones",
  auth("view_master_data"),
  MasterDataController.getAllTimezones
);

/**
 * @swagger
 * /master-data/facilities:
 *   get:
 *     summary: Get all facilities with specific attributes
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of facilities with facility_id and facility_type.
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - facility_id: "64b8f0e2d123e4567890abcd"
 *                   facility_type: "Hospital"
 */
router.get(
  "/facilities",
  auth("view_master_data"),
  FacilityController.fetch
);

/**
 * @swagger
 * /master-data/languages:
 *   get:
 *     summary: Retrieve all active languages
 *     tags: [MasterData]
 *     responses:
 *       200:
 *         description: List of active languages.
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - language_id: "123e4567-e89b-12d3-a456-426614174000"
 *                   name: "English"
 *                   code: "en"
 *                   default: true
 */
router.get(
  "/languages",
  MasterDataController.getLanguages
);

/**
 * @swagger
 * /master-data/access-levels:
 *   get:
 *     summary: Get all access levels (id and name) that are referenced in FacilityAccessLevel with optional filters
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: default_access_guest
 *         schema:
 *           type: boolean
 *         description: "Filter for FacilityAccessLevel.default_access_guest (default: true)"
 *       - in: query
 *         name: requestable_guest
 *         schema:
 *           type: boolean
 *         description: "Filter for FacilityAccessLevel.requestable_guest (default: true)"
 *       - in: query
 *         name: facility_id
 *         schema:
 *           type: string
 *         description: "Filter for AccessLevel of a specific facility (optional)"
 *     responses:
 *       200:
 *         description: List of access levels
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - access_level_id: "64b8f0e2d123e4567890abcd"
 *                   name: "Admin"
 */
router.get(
  "/access-levels",
  auth("view_master_data"),
  validate(MasterDataValidation.getAllAccessLevel),
  MasterDataController.getAllAccessLevel
);

/**
 * @swagger
 * /master-data/identity-hub:
 *   get:
 *     summary: Search identities by name, eid, or search string
 *     tags: [MasterData]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search string for name or eid
 *     responses:
 *       200:
 *         description: List of matching identities
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 - first_name: "John"
 *                   last_name: "Doe"
 *                   eid: "EMP123"
 */
router.get(
  "/identity-hub",
  auth("view_master_data"),
  validate(MasterDataValidation.identityHub),
  MasterDataController.getIdentityHub
);

module.exports = router;
