'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_patient_history AS
      SELECT
        h.created_at AS effective_date,
        h.patient_history_id,
        h.column_name AS field_changes,
        h.old_value,
        h.new_value,
        t.operation AS event_type,
        t.patient_id AS patient_id,
        t.updated_by AS modified_by
      FROM patient_history h
      JOIN patient_transaction t
        ON h.patient_transaction_id = t.patient_transaction_id;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS view_patient_history;`
    );
  },
};
