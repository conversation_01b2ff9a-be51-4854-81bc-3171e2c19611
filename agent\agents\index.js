const fs = require("fs");
const path = require("path");
const { titleCase } = require("../helpers/global.helper");
const agents = {};

// Get the current directory where the agent files reside.
const agentsDirectory = __dirname;

// Read all files in the agents directory that end with ".agent.js", excluding this index file.
fs.readdirSync(agentsDirectory)
  .filter((file) => file.endsWith(".agent.js") && file !== "index.js")
  .forEach((file) => {
    // Use titleCase (or any naming convention) to set the agent name.
    const agentName = titleCase(path.basename(file, ".agent.js"));
    const agentModule = require(path.join(agentsDirectory, file));
    agents[agentName] = agentModule;
  });
  
// console.log(agents)

module.exports = agents;
