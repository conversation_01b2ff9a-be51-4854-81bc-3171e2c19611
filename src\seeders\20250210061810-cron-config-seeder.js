"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface) => {
    const now = new Date();
    await queryInterface.bulkInsert("cron_config", [
      {
        cron_config_id: uuidv4(),
        name: "inbound_agent",
        display_name: "Inbound Agent: For CSV",
        description: "Inbound agent for taking CSV files and processing them",
        schedule: "0 * * * *", // Every hour
        // schedule: "*/1 * * * *", // Every minute
        is_active: true,
        created_at: now,
        updated_at: now,
      },
    ]);
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete("cron_config", null, {});
  },
};
