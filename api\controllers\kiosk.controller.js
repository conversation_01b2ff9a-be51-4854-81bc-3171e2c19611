const { DeviceSetting, Device, AppointmentGuest, PatientGuest, Patient, Appointment, Facility, PatientAppointmentView } = require("../models");
const { sequelize } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");
const { Op } = require("sequelize");

/**
 * Get device setting by device_id.
 *
 * @async
 * @function getDeviceSetting
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the device setting or 404 if not found.
 */
exports.getDeviceSetting = catchAsync(async (req, res) => {
  const { device_id } = req.params;
  const deviceSetting = await DeviceSetting.findOne({ where: { device_id } });
  sendSuccess(res, "Device setting retrieved successfully", httpStatus.OK, deviceSetting);
});

/**
 * Get all device settings/templates.
 *
 * @async
 * @function getDeviceTemplate
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends all device settings or empty array if none found.
 */
exports.getDeviceTemplate = catchAsync(async (req, res) => {
  const devices = await DeviceSetting.findAll();
  sendSuccess(res, "Device templates retrieved successfully", httpStatus.OK, devices);
});

/**
 * Fetch guest by PIN and Name with device validation.
 *
 * @async
 * @function fetchGuestByPin
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the guest information or error if not found.
 */
exports.fetchGuestByPin = catchAsync(async (req, res) => {
  const { device_id, guest_pin, guest_name } = req.body;
  const device = await Device.findOne({
    where: { device_id },
    attributes: ['facility_id'],
  });
  if (!device) {
    return sendError(res, "Invalid device ID", httpStatus.BAD_REQUEST);
  }
  const nameConditions = [];
  if (guest_name && guest_name.trim()) {
    const nameParts = guest_name.trim().split(/\s+/);
    if (nameParts.length === 1) {
      nameConditions.push(
        { first_name: { [Op.iLike]: `%${nameParts[0]}%` } },
        { last_name: { [Op.iLike]: `%${nameParts[0]}%` } }
      );
    } else {
      nameConditions.push(
        {
          [Op.and]: [
            { first_name: { [Op.iLike]: `%${nameParts[0]}%` } },
            { last_name: { [Op.iLike]: `%${nameParts.slice(1).join(' ')}%` } }
          ]
        },
        {
          [Op.and]: [
            { first_name: { [Op.iLike]: `%${nameParts.slice(0, -1).join(' ')}%` } },
            { last_name: { [Op.iLike]: `%${nameParts[nameParts.length - 1]}%` } }
          ]
        }
      );
    }
  }
  const whereConditions = {
    guest_pin,
    status: { [Op.in]: [0, 4] },
  };
  if (device.facility_id) {
    whereConditions.facility_id = device.facility_id;
  }
  const appointmentGuest = await AppointmentGuest.findOne({
    where: whereConditions,
    include: [
      {
        model: PatientGuest,
        as: 'patientGuest',
        where: nameConditions.length > 0 ? { [Op.or]: nameConditions } : {},
        attributes: ['first_name', 'last_name'],
        required: true,
      }
    ],
    attributes: ['appointment_guest_id'],
  });

  if (!appointmentGuest) {
    return sendError(
      res,
      "No guest found with the provided PIN and name in Pending Check-In or Registered status",
      httpStatus.NOT_FOUND
    );
  }
  const responseData = {
    appointment_guest_id: appointmentGuest.appointment_guest_id,
    guest_name: `${appointmentGuest.patientGuest.first_name} ${appointmentGuest.patientGuest.last_name}`,
  };
  sendSuccess(res, "Guest found successfully", httpStatus.OK, responseData);
});

/**
 * Get patient details and appointment guests by appointment_guest_id and phone last 4 digits.
 *
 * @async
 * @function getPatientByAppointmentAndPhone
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends patient details and appointment guests or error if not found.
 */
exports.getPatientByAppointmentAndPhone = catchAsync(async (req, res) => {
  const { device_id, appointment_guest_id, phone_last_4 } = req.body;
  const device = await Device.findOne({
    where: { device_id },
    attributes: ['device_id', 'name', 'facility_id'],
    include: [
      {
        model: Facility,
        as: 'facility',
        attributes: ['facility_id', 'name'],
      }
    ]
  });
  if (!device) {
    return sendError(res, "Invalid device ID", httpStatus.BAD_REQUEST);
  }
  const appointmentGuest = await AppointmentGuest.findOne({
    where: {
      appointment_guest_id,
      ...(device.facility_id && { facility_id: device.facility_id }) // Filter by facility if device has one
    },
    include: [
      {
        model: PatientGuest,
        as: 'patientGuest',
        attributes: ['patient_guest_id', 'first_name', 'last_name', 'email', 'birth_date', 'relationship_type']
      }
    ],
    attributes: ['appointment_guest_id', 'appointment_id', 'status']
  });
  if (!appointmentGuest) {
    return sendError(
      res,
      "No appointment guest found with the provided ID in the specified facility",
      httpStatus.NOT_FOUND
    );
  } // Get the appointment and patient details separately
  const appointment = await Appointment.findOne({
    where: { appointment_id: appointmentGuest.appointment_id },
    include: [
      {
        model: Patient,
        as: 'patient',
        attributes: ['patient_id', 'first_name', 'last_name', 'phone'],
        where: {
          phone: {
            [Op.like]: `%${phone_last_4}` // Match last 4 digits of phone
          }
        }
      }
    ],
    attributes: ['appointment_id', 'appointment_date']
  });

  if (!appointment) {
    return sendError(
      res,
      "No appointment found with the provided phone number last 4 digits",
      httpStatus.NOT_FOUND
    );
  }

  // Get all appointment guests for the same patient to show related guests
  const allAppointmentGuests = await AppointmentGuest.findAll({
    where: {
      appointment_id: {
        [Op.in]: await Appointment.findAll({
          where: { patient_id: appointment.patient.patient_id },
          attributes: ['appointment_id'],
          raw: true
        }).then(appointments => appointments.map(a => a.appointment_id))
      },
      ...(device.facility_id && { facility_id: device.facility_id })
    },
    include: [
      {
        model: PatientGuest,
        as: 'patientGuest',
        attributes: ['patient_guest_id', 'first_name', 'last_name', 'email', 'birth_date', 'relationship_type']
      }
    ],
    attributes: ['appointment_guest_id']
  });

  // Prepare response data
  const responseData = {
    patient_name: `${appointment.patient.first_name} ${appointment.patient.last_name}`,
    facility_name: device.facility ? device.facility.name : 'No facility assigned',
    facility_id: device.facility_id,
    patient_details: {
      patient_id: appointment.patient.patient_id,
      first_name: appointment.patient.first_name,
      last_name: appointment.patient.last_name,
      phone: appointment.patient.phone
    },
    appointment_guests: allAppointmentGuests.map(guest => ({
      appointment_guest_id: guest.appointment_guest_id,
      guest_name: `${guest.patientGuest.first_name} ${guest.patientGuest.last_name}`,
      birth_date: guest.patientGuest.birth_date,
      email: guest.patientGuest.email,
      relation: guest.patientGuest.relationship_type || 'Unknown'
    }))
  };

  sendSuccess(res, "Patient and appointment guests retrieved successfully", httpStatus.OK, responseData);
});

/**
 * Get outpatient details by cellphone number and birth date.
 *
 * @async
 * @function getOutpatientDetails
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends outpatient details or error if not found.
 */
exports.getOutpatientDetails = catchAsync(async (req, res) => {
  const { device_id, cellphone, birth_date } = req.body;
  const device = await Device.findOne({
    where: { device_id },
    attributes: ['device_id', 'name', 'facility_id'],
    include: [
      {
        model: Facility,
        as: 'facility',
        attributes: ['facility_id', 'name'],
      }
    ]
  });
  if (!device) {
    return sendError(res, "Invalid device ID", httpStatus.BAD_REQUEST);
  }
  // Find outpatient appointments using PatientAppointmentView
  const outpatientAppointments = await PatientAppointmentView.findAll({
    where: {
      type: 1, // 1 = Outpatient (from master data)
      phone: {
        [Op.like]: `%${cellphone}%` // Match cellphone number
      },
      birth_date: birth_date, // Exact match on birth date
      ...(device.facility_id && { facility_id: device.facility_id }) // Filter by facility if device has one
    },
    attributes: [
      'appointment_id',
      'appointment_date',
      'type',
      'appointment_type_name',
      'patient_id',
      'first_name',
      'last_name',
      'phone',
      'birth_date',
      'facility_id',
      'facility_name'
    ],
    order: [['appointment_date', 'DESC']] // Most recent appointments first
  });
  if (!outpatientAppointments || outpatientAppointments.length === 0) {
    return sendError(
      res,
      "No outpatient appointments found with the provided cellphone number and birth date",
      httpStatus.NOT_FOUND
    );
  }
  const responseData = {
    facility_name: outpatientAppointments[0].facility_name || 'No facility assigned',
    facility_id: outpatientAppointments[0].facility_id,
    patient_details: {
      patient_id: outpatientAppointments[0].patient_id,
      name: `${outpatientAppointments[0].first_name} ${outpatientAppointments[0].last_name}`,
      birth_date: outpatientAppointments[0].birth_date,
      phone: outpatientAppointments[0].phone
    },
    outpatient_appointments: outpatientAppointments.map(appointment => ({
      appointment_id: appointment.appointment_id,
      appointment_date: appointment.appointment_date,
      patient_name: `${appointment.first_name} ${appointment.last_name}`,
      birth_date: appointment.birth_date
    }))
  };
 sendSuccess(res, "Outpatient details retrieved successfully", httpStatus.OK, responseData);
});

/**
 * Perform patient and guest check-in together.
 *
 * @async
 * @function performPatientGuestCheckin
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends check-in confirmation or error if failed.
 */
exports.performPatientGuestCheckin = catchAsync(async (req, res) => {
  const { device_id, appointment_id, appointment_guest_id } = req.body;
  const transaction = await sequelize.transaction();
  try {
    const device = await Device.findOne({
      where: { device_id },
      attributes: ['device_id', 'name', 'facility_id'],
      include: [
        {
          model: Facility,
          as: 'facility',
          attributes: ['facility_id', 'name'],
        }
      ]
    });
    if (!device) {
      await transaction.rollback();
      return sendError(res, "Invalid device ID", httpStatus.BAD_REQUEST);
    }
    const appointment = await PatientAppointmentView.findOne({
      where: {
        appointment_id,
        ...(device.facility_id && { facility_id: device.facility_id }) // Filter by facility if device has one
      },
      attributes: ['appointment_id', 'patient_id', 'appointment_date', 'appointment_status', 'first_name', 'last_name', 'phone']
    });

    if (!appointment) {
      await transaction.rollback();
      return sendError(
        res,
        "Appointment not found or not accessible from this device's facility",
        httpStatus.NOT_FOUND
      );
    } // Find and validate the appointment guest
    const appointmentGuest = await AppointmentGuest.findOne({
      where: {
        appointment_guest_id,
        appointment_id, // Ensure guest belongs to the same appointment
        ...(device.facility_id && { facility_id: device.facility_id })
      },
      include: [
        {
          model: PatientGuest,
          as: 'patientGuest',
          attributes: ['patient_guest_id', 'first_name', 'last_name', 'email', 'phone']
        }
      ],
      attributes: ['appointment_guest_id', 'status', 'guest_pin'],
      transaction
    });
    if (!appointmentGuest) {
      await transaction.rollback();
      return sendError(
        res,
        "Appointment guest not found or not associated with the provided appointment",
        httpStatus.NOT_FOUND
      );
    }
    // Check if patient is already checked in
    if (appointment.status === 3) { // Assuming 2 = Checked In from master data
      await transaction.rollback();
      return sendError(
        res,
        "Patient is already checked in",
        httpStatus.BAD_REQUEST
      );
    }
    // Check if guest is already checked in
    if (appointmentGuest.status === 2) { // Assuming 2 = Checked In from master data
      await transaction.rollback();
      return sendError(
        res,
        "Guest is already checked in",
        httpStatus.BAD_REQUEST
      );
    }
    await Appointment.update(
      {
        status: 3, // Arrived
        updated_at: new Date()
      },
      {
        where: { appointment_id },
        transaction
      }
    );
    await AppointmentGuest.update(
      {
        status: 2, // Checked In
        updated_at: new Date()
      },
      {
        where: { appointment_guest_id },
        transaction
      }
    );
    await transaction.commit();
    const responseData = {
      facility_name: device.facility ? device.facility.name : 'No facility assigned',
      facility_id: device.facility_id,
      check_in_time: new Date(),
      patient_details: {
        appointment_id: appointment.appointment_id,
        patient_id: appointment.patient_id,
        patient_name: `${appointment.first_name} ${appointment.last_name}`,
        appointment_date: appointment.appointment_date,
        status: appointment.appointment_status
      },
      guest_details: {
        appointment_guest_id: appointmentGuest.appointment_guest_id,
        guest_name: `${appointmentGuest.patientGuest.first_name} ${appointmentGuest.patientGuest.last_name}`,
        guest_pin: appointmentGuest.guest_pin,
        email: appointmentGuest.patientGuest.email,
        phone: appointmentGuest.patientGuest.phone,
        status: appointmentGuest.status,
      }
    };
    sendSuccess(res, "Patient and guest checked in successfully", httpStatus.OK, responseData);
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
});

/**
 * Get inpatient appointment details by device, facility, phone last 4 digits, and name first 3 digits.
 *
 * @async
 * @function getInpatientAppointmentDetails
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends inpatient appointment details or error if not found.
 */
exports.getInpatientAppointmentDetails = catchAsync(async (req, res) => {
  const { device_id, facility_id, phone_last_4, first_name_first_3, last_name_first_3 } = req.body;
  const device = await Device.findOne({
    where: { device_id },
    attributes: ['device_id', 'name', 'facility_id'],
    include: [
      {
        model: Facility,
        as: 'facility',
        attributes: ['facility_id', 'name'],
      }
    ]
  });
  if (!device) {
    return sendError(res, "Invalid device ID", httpStatus.BAD_REQUEST);
  }
  if (device.facility_id && device.facility_id !== facility_id) {
    return sendError(
      res,
      "Facility ID does not match device's assigned facility",
      httpStatus.BAD_REQUEST
    );
  }
  const inpatientAppointments = await PatientAppointmentView.findAll({
    where: {
      type: 0, // 0 = Inpatient (from master data)
      facility_id: facility_id,
      phone: {
        [Op.like]: `%${phone_last_4}` // Match last 4 digits of phone
      },
      first_name: {
        [Op.iLike]: `${first_name_first_3}%` // Match first 3 characters of first name (case insensitive)
      },
      last_name: {
        [Op.iLike]: `${last_name_first_3}%` // Match first 3 characters of last name (case insensitive)
      }
    },
    attributes: [
      'appointment_id',
      'first_name',
      'last_name',
      'facility_name'
    ],
    order: [['appointment_date', 'DESC']] // Most recent appointments first
  });
  if (!inpatientAppointments || inpatientAppointments.length === 0) {
    return sendError(
      res,
      "No inpatient appointments found with the provided criteria",
      httpStatus.NOT_FOUND
    );
  }
  const responseData = inpatientAppointments.map(appointment => ({
    appointment_id: appointment.appointment_id,
    patient_name: `${appointment.first_name} ${appointment.last_name}`,
    facility_name: appointment.facility_name
  }));
  sendSuccess(res, "Inpatient appointment details retrieved successfully", httpStatus.OK, responseData);
});


