const rateLimit = require('express-rate-limit');
const config = require('../config/config');

/**
 * Rate limiter that adapts based on authentication mode
 * - In Tyk mode: Minimal rate limiting (Tyk handles it)
 * - In Custom mode: Standard rate limiting
 */
const createRateLimiter = () => {
  if (config.auth.mode === 'tyk') {
    // Tyk mode - minimal rate limiting as Tyk Gateway handles it
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 50000, // Very high limit since Tyk handles rate limiting
      message: {
        status: false,
        message: 'Rate limit exceeded. This should be handled by Tyk Gateway.',
        error: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting if Tyk headers are present and valid
        return req.headers['x-caremate-authorized'] === 'true';
      }
    });
  } else {
    // Custom mode - standard rate limiting
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 10000, // Standard limit for direct API access
      message: {
        status: false,
        message: 'Too many requests from this IP, please try again later.',
        error: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        // Use identity ID if available, otherwise fall back to IP
        return req.identity?.identity_id || req.ip;
      }
    });
  }
};

/**
 * Static rate limiter configurations for different use cases
 */
const rateLimiters = {
  // Standard API rate limiter
  api: createRateLimiter(),

  // Strict rate limiter for authentication endpoints
  auth: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: config.auth.mode === 'tyk' ? 1000 : 100, // Higher limit in Tyk mode
    message: {
      status: false,
      message: 'Too many authentication attempts, please try again later.',
      error: 'AUTH_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),

  // Lenient rate limiter for public endpoints
  public: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: config.auth.mode === 'tyk' ? 100000 : 20000,
    message: {
      status: false,
      message: 'Too many requests, please try again later.',
      error: 'PUBLIC_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
  })
};

// Export default rate limiter for backward compatibility
module.exports = rateLimiters.api;

// Export all rate limiters
module.exports.api = rateLimiters.api;
module.exports.auth = rateLimiters.auth;
module.exports.public = rateLimiters.public;
module.exports.rateLimiters = rateLimiters;

