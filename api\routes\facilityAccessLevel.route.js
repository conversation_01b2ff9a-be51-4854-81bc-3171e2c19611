
const express = require("express");
const validate = require("../middlewares/validate");
const { FacilityAccessLevelValidation } = require("../validations");
const { FacilityAccessLevelController } = require("../controllers");
const auth = require("../middlewares/auth");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * /facility/access-levels/{facilityId}:
 *   get:
 *     summary: Get all facility access levels (paginated)
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID for which access levels are retrieved.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *     responses:
 *       200:
 *         description: Paginated list of facility access levels.
 */
router.get(
  "/",
  auth("view_facility_access_levels"),
  validate(FacilityAccessLevelValidation.facility),
  FacilityAccessLevelController.index
);

/**
 * @swagger
 * /facility/access-levels/{facilityId}/{facility_access_level_id}:
 *   get:
 *     summary: Get facility access level by ID
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The facility ID.
 *       - in: path
 *         name: facility_access_level_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The facility access level ID.
 *     responses:
 *       200:
 *         description: Facility access level details.
 *       404:
 *         description: Facility access level not found.
 */
router.get(
  "/:facility_access_level_id",
  auth("facility_access_level_details"),
  validate(FacilityAccessLevelValidation.facilityAccessLevel),
  FacilityAccessLevelController.show
);

/**
 * @swagger
 * /facility/access-levels/{facilityId}:
 *   post:
 *     summary: Create a new facility access level
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID where the access level will be created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             access_level_id: "5ebac534-954b-5413-9806-c11200000001"
 *             building_id: "64b8f0e2-d123-e456-7890-abcd12345678"
 *             floor_id: "64b8f0e2-d123-e456-7890-efgh12345678"
 *             room_id: "64b8f0e2-d123-e456-7890-ijkl12345678"
 *             entry_restrictions: "No entry after 10pm"
 *             access_protocol: "Swipe card required"
 *             requestable_guest: true
 *             default_access_guest: false
 *             default_access_identity: true
 *             identity_type: ["COS", "EMP"]
 *     responses:
 *       201:
 *         description: Facility access level created successfully.
 */
router.post(
  "/",
  auth("create_facility_access_level"),
  validate(FacilityAccessLevelValidation.create),
  FacilityAccessLevelController.create
);

/**
 * @swagger
 * /facility/access-levels/{facilityId}/{facility_access_level_id}:
 *   patch:
 *     summary: Update an existing facility access level
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The facility ID.
 *       - in: path
 *         name: facility_access_level_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The facility access level ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               entry_restrictions:
 *                 type: string
 *                 example: "Updated entry restrictions"
 *               access_protocol:
 *                 type: string
 *                 example: "Updated access protocol"
 *               requestable_guest:
 *                 type: boolean
 *                 example: false
 *               default_access_guest:
 *                 type: boolean
 *                 example: true
 *               default_access_identity:
 *                 type: boolean
 *                 example: false
 *               identity_type:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: ["COS", "EMP"]
 *                 example: ["EMP"]
 *               building_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               floor_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               room_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *             minProperties: 1
 *     responses:
 *       200:
 *         description: Facility access level updated successfully.
 *       404:
 *         description: Facility access level not found.
 *       400:
 *         description: Bad request - validation error.
 */
router.patch(
  "/:facility_access_level_id",
  auth("edit_facility_access_level"),
  validate(FacilityAccessLevelValidation.update),
  FacilityAccessLevelController.update
);

/**
 * @swagger
 * /facility/access-levels/{facilityId}/{facility_access_level_id}:
 *   delete:
 *     summary: Delete a facility access level
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The facility ID.
 *       - in: path
 *         name: facility_access_level_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The facility access level ID.
 *     responses:
 *       200:
 *         description: Facility access level deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Facility access level deleted successfully"
 *       404:
 *         description: Facility access level not found.
 *       400:
 *         description: Bad request - validation error.
 */
router.delete(
  "/:facility_access_level_id",
  auth("delete_facility_access_level"),
  validate(FacilityAccessLevelValidation.remove),
  FacilityAccessLevelController.remove
);

module.exports = router;
