const httpStatus = require("http-status");
const { Guest, GuestVisit, Visit, Identity, Facility } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");

/**
 * @desc    Create a new guest
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the created guest
 */
exports.createGuest = catchAsync(async (req, res) => {
  const guest = await Guest.create(req.body);
  sendSuccess(res, "Guest created successfully", httpStatus.CREATED, guest);
});

/**
 * @desc    Get all guests with pagination, sorting, and filtering
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing paginated guests
 */
exports.getGuests = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    sortBy = "created_at", // Must match the actual column name in the DB
    sortOrder = "DESC",
    search,
  } = req.query;

  const paginationOptions = {
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    sortBy,
    sortOrder,
  };

  // Build where conditions
  const whereConditions = {}

  // Simple and direct search condition
  if (search) {
    const searchTerm = String(search).trim();

    if (searchTerm.length > 0) {
      // Try both iLike (PostgreSQL) and like (MySQL) for compatibility
      whereConditions[Op.or] = [
        { first_name: { [Op.iLike]: `%${searchTerm}%` } },
        { last_name: { [Op.iLike]: `%${searchTerm}%` } },
      ];
      console.log('WHERE CONDITIONS ADDED:', JSON.stringify(whereConditions, null, 2));

      // Alternative for MySQL if iLike doesn't work
      // whereConditions[Op.or] = [
      //   { first_name: { [Op.like]: `%${searchTerm}%` } },
      //   { last_name: { [Op.like]: `%${searchTerm}%` } },
      // ];
    } else {
      console.log('Search term is empty after trim');
    }
  } else {
    console.log('No search parameter provided');
  }

  console.log('Final whereConditions:', JSON.stringify(whereConditions, null, 2));

  // Build query options with proper structure
  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
  };

  // Add where conditions if any exist
  if (Object.keys(whereConditions).length > 0) {
    queryOptions.where = whereConditions;
    console.log('WHERE CONDITIONS APPLIED TO QUERY');
  } else {
    console.log('NO WHERE CONDITIONS - WILL RETURN ALL RECORDS');
  }

  console.log('Final queryOptions:', JSON.stringify(queryOptions, null, 2));
  console.log('=== END SEARCH DEBUG ===');

  // Test with direct query first
  if (search && search.trim()) {
    console.log('TESTING DIRECT QUERY...');
    const directResult = await Guest.findAll({
      where: {
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${search.trim()}%` } },
          { last_name: { [Op.iLike]: `%${search.trim()}%` } },
        ]
      },
      order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
      limit: parseInt(limit, 10),
      offset: (parseInt(page, 10) - 1) * parseInt(limit, 10),
    });
    console.log('DIRECT QUERY RESULT COUNT:', directResult.length);

    const totalCount = await Guest.count({
      where: {
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${search.trim()}%` } },
          { last_name: { [Op.iLike]: `%${search.trim()}%` } },
        ]
      }
    });

    const result = {
      data: directResult,
      totalItems: totalCount,
      totalPages: Math.ceil(totalCount / parseInt(limit, 10)),
      currentPage: parseInt(page, 10),
    };

    return sendSuccess(res, "Guests retrieved successfully", httpStatus.OK, result);
  }

  // Use paginate for non-search queries
  const result = await paginate(Guest, queryOptions, paginationOptions);
  sendSuccess(res, "Guests retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc    Get a single guest by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the guest
 */
exports.getGuestById = catchAsync(async (req, res) => {
  const { guest_id } = req.params;
  const guest = await Guest.findByPk(guest_id);
  if (!guest) {
    return sendError(res, "Guest not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Guest retrieved successfully", httpStatus.OK, guest);
});

/**
 * @desc    Update a guest by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated guest
 */
exports.updateGuest = catchAsync(async (req, res) => {
  const { guest_id } = req.params;
  const guest = await Guest.findByPk(guest_id);
  if (!guest) {
    return sendError(res, "Guest not found", httpStatus.NOT_FOUND);
  }
  await guest.update(req.body);
  sendSuccess(res, "Guest updated successfully", httpStatus.OK, guest);
});

/**
 * @desc    Delete a guest by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteGuest = catchAsync(async (req, res) => {
  const { guest_id } = req.params; 
  const guest = await Guest.findByPk(guest_id);
  if (!guest) {
    return sendError(res, "Guest not found", httpStatus.NOT_FOUND);
  } await guest.destroy();
  sendSuccess(res, "Guest deleted successfully", httpStatus.OK, { guest_id: guest_id });
});

/**
 * @desc    Search guests by email or phone
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing matching guests
 */
exports.searchGuests = catchAsync(async (req, res) => {
  const { email, phone, first_name, last_name } = req.query;
  if (!email && !phone && !first_name && !last_name) {
    return sendError(res, "At least one search parameter (email, phone, first_name, last_name) is required", httpStatus.BAD_REQUEST);
  }
  const whereConditions = {};
  if (email) {
    whereConditions.email = { [Op.iLike]: `%${email}%` };
  }
  if (phone) {
    whereConditions.mobile_phone = { [Op.iLike]: `%${phone}%` };
  }
  if (first_name) {
    whereConditions.first_name = { [Op.iLike]: `%${first_name}%` };
  }
  if (last_name) {
    whereConditions.last_name = { [Op.iLike]: `%${last_name}%` };
  }
  const guests = await Guest.findAll({
    where: whereConditions,
    attributes: ["guest_id", "first_name", "last_name", "email", "mobile_phone", "company", "private_visitor"],
    order: [["first_name", "ASC"], ["last_name", "ASC"]],
  });
  sendSuccess(res, "Guests search completed", httpStatus.OK, guests);
});

// ==================== VISIT MANAGEMENT FUNCTIONS ====================

/**
 * @desc    Create a new visit
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the created visit
 */
exports.createVisit = catchAsync(async (req, res) => {
  const visit = await Visit.create(req.body);
  sendSuccess(res, "Visit created successfully", httpStatus.CREATED, visit);
});

/**
 * @desc    Get all visits with pagination, sorting, and filtering
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing paginated visits
 */
exports.getVisits = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    sortBy = "created_at",
    sortOrder = "DESC",
    facility_id,
    host_id,
    status,
    start_date,
  } = req.query;
  const paginationOptions = {
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    sortBy,
    sortOrder,
  };

  const whereConditions = {};

  if (facility_id) {
    whereConditions.facility_id = facility_id;
  }

  if (host_id) {
    whereConditions.host_id = host_id;
  }


  if (start_date) {
    whereConditions.start_date = start_date;
  }

  // Build query options with proper structure
  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    include: [
      {
        model: Facility,
        as: "facility",
        attributes: ["facility_id", "name"],
      },
      {
        model: Identity,
        as: "host",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
      {
        model: Identity,
        as: "escort",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
      {
        model: GuestVisit,
        as: "guestVisits",
        attributes: ["guest_visit_id", "guest_status", "check_in_time", "check_out_time"],
        include: [
          {
            model: Guest,
            as: "guest",
            attributes: ["guest_id", "first_name", "last_name", "email", "mobile_phone"],
          },
        ],
      },
    ],
  };

  // Add where conditions if any exist
  if (Object.keys(whereConditions).length > 0) {
    queryOptions.where = whereConditions;
  }

  const result = await paginate(Visit, queryOptions, paginationOptions);
  sendSuccess(res, "Visits retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc    Get a single visit by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the visit
 */
exports.getVisitById = catchAsync(async (req, res) => {
  const { visit_id } = req.params;

  const visit = await Visit.findByPk(visit_id, {
    include: [
      {
        model: Facility,
        as: "facility",
        attributes: ["facility_id", "name"],
      },
      {
        model: Identity,
        as: "host",
        attributes: ["identity_id", "first_name", "last_name", "email", "mobile"],
      },
      {
        model: Identity,
        as: "escort",
        attributes: ["identity_id", "first_name", "last_name", "email", "mobile"],
      },
      {
        model: GuestVisit,
        as: "guestVisits",
        attributes: ["guest_visit_id", "guest_status", "check_in_time", "check_out_time", "notes"],
        include: [
          {
            model: Guest,
            as: "guest",
            attributes: ["guest_id", "first_name", "last_name", "email", "mobile_phone", "company"],
          },
        ],
      },
    ],
  });

  if (!visit) {
    return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Visit retrieved successfully", httpStatus.OK, visit);
});

/**
 * @desc    Update a visit by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated visit
 */
exports.updateVisit = catchAsync(async (req, res) => {
  const { visit_id } = req.params;

  const visit = await Visit.findByPk(visit_id);
  if (!visit) {
    return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
  }

  await visit.update(req.body);
  sendSuccess(res, "Visit updated successfully", httpStatus.OK, visit);
});

/**
 * @desc    Delete a visit by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteVisit = catchAsync(async (req, res) => {
  const { visit_id } = req.params;

  const visit = await Visit.findByPk(visit_id);
  if (!visit) {
    return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
  }

  await visit.destroy();
  sendSuccess(res, "Visit deleted successfully", httpStatus.OK, { visit_id });
});
