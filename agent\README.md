# Processor
## Project Structure

```
project-root/
├── config/
│   ├── database.js
│   ├── passport.js
│   ├── config.js
│   ├── logger.js
│   ├── morgan.js
│   └── rabbitmq.js
├── middlewares/
│   ├── auth.js
│   ├── error.js
│   └── rateLimiter.js
├── models/
│   ├── identity.model.js
│   ├── accessLevel.model.js
│   └── permission.model.js
├── seeders/
│   └── 20250210061754-event-config-seeder.js
├── services/
│   ├── auth.service.js
│   └── event.service.js
├── utils/
│   ├── ApiError.js
│   └── ApiResponse.js
├── .env.local
├── .env.dev
├── .gitignore
├── .sequelizerc
├── app.js
├── server.js
├── package.json
├── package-lock.json
└── README.md
```

## Naming Conventions

- **Folders:** Lowercase, use plural nouns (e.g., `controllers/`, `models/`).
- **Configuration Files:** Lowercase, camel-case (e.g., `database.js`).
- **Model Files:** Lowercase, words separated by dots, use singular nouns (e.g., `identity.model.js`).
- **Controller Files:** Lowercase, words separated by dots, use singular nouns (e.g., `identity.controller.js`).
- **Services Files:** Lowercase, words separated by dots, use singular nouns (e.g., `auth.service.js`).
- **Route Files:** Lowercase, camel-case, use singular nouns (e.g., `identityRoute.js`).
- **Middleware Files:** Lowercase, camel-case (e.g., `rateLimiter.js`).


## Description of Directories

- **config/**: Contains configuration files, such as database configurations.
- **controllers/**: Houses functions that handle incoming requests and responses.
- **models/**: Defines data schemas and interacts with the database.
- **routes/**: Defines application routes and associates them with controller functions.
- **middlewares/**: Contains middleware functions for tasks like authentication.
- **services/**: Encapsulates business logic and external service interactions.
- **utils/**: Includes utility functions and helpers used across the application.
- **tests/**: Contains unit and integration tests, organized respectively.

## Add your files

```
cd existing_repo
git add .
git commit -m "Commit message"
git remote add origin https://git.onetalkhub.com/care/processor.git
git branch -M main
git push -uf origin main
```

## Development Setup and Run

- Basic setup and installation
```
npm install
npm run dev
```
- Create enviorment file with name `.env` and put environment variables:
```
NODE_ENV
PORT
DB_URL
JWT_SECRET
```

## Command for starting the HL7 project on server using PM2 service
```pm2 start ecosystem.config.js --only hl7_parser```

## Command for starting the Patient Admission Processor on server using PM2 service
```pm2 start ecosystem.config.js --only patient_admission_procesor```

## Command for starting the Email Sending Queue on server using PM2 service
```pm2 start ecosystem.config.js --only email_procesor```

## For syncing the database
```npm run db```

## For syncing the database with particular enviorment
```npm run db -- --env-file .env.dev```

## For refreshing the database (remove all tables and then re-create)
```npm run db:refresh```

## For refreshing the database with particular enviorment (remove all tables and then re-create)
```npm run db:refresh -- --env-file .env.dev```

## For refreshing the particular models
```npm run db:refresh -- --model StagingData Facility Floor Room```

## For running the migrations
```npx sequelize-cli db:migrate```

## For undo the migrations
```npx sequelize-cli db:migrate:undo```

## For seeding all seeder 
```npx sequelize-cli db:seed:all```

## For reverting the seeded data from a particular seeder 
```npx sequelize-cli db:seed:undo:all```

## For seeding a particular seeder 
```npx sequelize-cli db:seed --seed 20230409123456-seed-users.js```

## For reverting the seeded data from a particular seeder 
```npx sequelize-cli db:seed:undo --seed 20250210061756-seed-permissions-admin-role.js```

## For seeding the data with particular enviorment (default 'local')
```npx sequelize-cli db:seed:all --env-file .env.development```

## Agent System

The agent system processes data through inbound and outbound agents. Agents are identified by unique program-friendly names (snake_case format).

### Running Agents

To run a specific agent:
```bash
# Run the automation script with a specific agent
node agent.js --agent local_connection_batch_100

# Or run the main process directly
node index.js --agent local_connection_batch_100
```

### Agent Types

- **Inbound Agents**: Process incoming data from various sources (FTP, S3, Azure, Local, URL)
- **Outbound Agents**:
  - **CSV Outbound**: Generate CSV files from database models
  - **API Outbound**: Send data to external systems via REST APIs

### Agent Naming Convention

Agent names use snake_case format for program-friendly identification:
- `local_connection_batch_100` - Local directory agent with batch size 100
- `ftp_connection_main` - Main FTP server connection
- `s3_connection_production` - Production S3 bucket connection
- `azure_connection_backup` - Backup Azure storage connection
- `api_2_outbound_external_partner` - API outbound agent for external partner
- `api_2_outbound_hr_system` - API outbound agent for HR management system

## API Outbound Agents

API Outbound Agents are a new type of outbound agent that send data to external systems via REST APIs instead of generating CSV files. These agents listen to RabbitMQ queues for events generated by the rule engine and transform the data according to mapping configurations before sending it to external APIs.

### Key Features

- **Event-Driven**: Listen to RabbitMQ queues for rule engine events
- **Data Transformation**: Use JSON mapping files to transform data to external API formats
- **Error Handling**: Comprehensive retry logic with exponential backoff
- **Performance Monitoring**: Specialized API performance tracking
- **Authentication**: Support for various authentication methods (API keys, tokens, etc.)
- **Validation**: Data validation before API transmission

### API Agent Configuration

API outbound agents require the following configuration:

```json
{
  "source": "API",
  "type": "Outbound",
  "handler": "sendApiData",
  "mapping": "externalPartnerApi",
  "queue": "api_2_outbound_queue",
  "settings": {
    "api_url": "https://api.external-partner.com/v1/employees",
    "api_key": "your_api_key_here",
    "timeout": 45000,
    "retries": 3
  }
}
```

### API Mapping Files

API mapping files define how data should be transformed and sent to external APIs:

```json
{
  "mappingType": "apiTransform",
  "apiConfig": {
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "X-API-Key": "{{api_key}}"
    },
    "timeout": 30000,
    "retries": 3
  },
  "dataTransform": {
    "type": "object",
    "properties": {
      "employee": {
        "id": "{{Identity.eid}}",
        "email": "{{Identity.email}}",
        "firstName": "{{Identity.first_name}}"
      }
    }
  }
}
```

### Running API Outbound Agents

To run an API outbound agent:

```bash
# Start API outbound agent
node index.js --agent api_2_outbound_external_partner

# The agent will start listening for events and remain running
```

### Queue Processing Flow

1. **Rule Engine** generates events and sends them to `api_2_outbound_queue`
2. **Processor** receives events and forwards them to appropriate API queues
3. **API Agent** listens to the queue and processes events
4. **Data Transformation** occurs according to mapping configuration
5. **API Call** is made to external system with retry logic
6. **Performance Metrics** are recorded and logged

### Performance Monitoring

API agents include specialized performance monitoring that tracks:
- Response times (min, max, average)
- Success/failure rates
- Status code distribution
- Error types and frequencies
- Retry attempts and timeouts
- API throughput metrics

Performance reports are saved to the `performances/` directory with detailed metrics.

### Available Agents

Use the seeder to see all available agents or check the database `agent` table for active agents.