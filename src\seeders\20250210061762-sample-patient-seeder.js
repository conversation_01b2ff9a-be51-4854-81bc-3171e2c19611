"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");
const { update } = require("lodash");
const logger = require("../config/logger");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Start a transaction
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const function_name = await queryInterface.sequelize.query(
        "SELECT function_id FROM function where name='hl7Processor';",
        { type: Sequelize.QueryTypes.SELECT }
      );
      // Create 100 patient records.
      const patients = Array.from({ length: 100 }).map(() => {
        const patient_id = uuidv4();
        return {
          patient_id, // using "id" as primary key according to the DDL
          title: faker.person.prefix(),
          first_name: faker.person.firstName(),
          middle_name: faker.person.middleName(),
          last_name: faker.person.lastName(),
          suffix: faker.person.suffix(),
          preferred_name: faker.person.firstName(),
          birth_date: faker.date.past({
            years: 50,
            refDate: new Date(2000, 0, 1),
          }),
          death_date: faker.date.future({ years: 10 }),
          gender: faker.helpers.arrayElement([0, 1, 2]),
          gender_identity: faker.word.adjective(),
          marital_status: faker.helpers.arrayElement([0, 1, 2]),
          race: faker.word.adjective(),
          ethnicity: faker.word.adjective(),
          preferred_language: faker.helpers.arrayElement([0, 1, 2, 3, 4]),
          birth_place: faker.location.city(),
          religion: faker.word.noun(),
          nationality: faker.location.country(),
          vip_status: faker.datatype.boolean(),
          consent_to_contact: faker.datatype.boolean(),
          primary_care_provider_id: uuidv4(),
          preferred_pharmacy_id: uuidv4(),
          confidentiality_code: faker.helpers.arrayElement([0, 1]),
          multiple_birth_indicator: faker.datatype.boolean(),
          birth_order: faker.number.int({ min: 1, max: 10 }),
          email:faker.internet.email(),
          phone: faker.phone.number().slice(0, 20),

          // image: faker.image.avatar(),
          // Audit fields
          active: faker.datatype.boolean(),
          created_at: new Date(),
          updated_at: new Date(),
          updated_by: function_name[0].function_id, // Assuming function_id is the ID of the user who created the record
        };
      });

      // Insert patients into the "patient" table.
      await queryInterface.bulkInsert("patient", patients, { transaction });

      // For each patient record, create an associated patient identifier.
      // Here, we generate one patient identifier per patient.
      const identifierTypes = [
        { key: 0, value: "MRN" },
        { key: 1, value: "SSN" },
        { key: 2, value: "DL" },
      ];

      const assigningAuthorities = [
        { key: 0, value: "HOSPITAL_A" },
        { key: 1, value: "SSA" },
      ];

      const patientIdentifiers = patients.map((patient) => {
        const identifierType = faker.helpers.arrayElement(identifierTypes);
        const assigningAuthority =
          faker.helpers.arrayElement(assigningAuthorities);
        return {
          patient_identifier_id: uuidv4(),
          patient_id: patient.patient_id, // foreign key from the patient record
          identifier_type: identifierType.key, // Use key from master_data
          identifier_value: faker.string.alphanumeric(10),
          assigning_authority: assigningAuthority.key, // Use key from master_data
          effective_from: new Date(), // can adjust to a specific date if necessary
          effective_to: null, // leave as null if no end date is defined
          active: true,
          created_at: new Date(), // Add created_at field
          updated_at: new Date(), // Add updated_at field
        };
      });

      // Insert patient identifiers into the "patient_identifier" table.
      await queryInterface.bulkInsert(
        "patient_identifier",
        patientIdentifiers,
        { transaction }
      );

      // Commit the transaction.
      await transaction.commit();
    } catch (error) {
      logger.error("Seeding error:", error);
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Start a transaction for rollback.
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Check if the tables exist before attempting to delete records.
      const tableCheckQuery = async (tableName) => {
        const [results] = await queryInterface.sequelize.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = :tableName
          ) AS "exists";`,
          { replacements: { tableName } }
        );
        return results[0].exists;
      };

      const patientIdentifierExists = await tableCheckQuery(
        "patient_identifier"
      );
      const patientExists = await tableCheckQuery("patient");

      // Delete patient identifiers first if the table exists.
      if (patientIdentifierExists) {
        await queryInterface.bulkDelete("patient_identifier", null, {
          transaction,
        });
      }

      // Delete patients if the table exists.
      if (patientExists) {
        await queryInterface.bulkDelete("patient", null, { transaction });
      }

      await transaction.commit();
    } catch (error) {
      logger.error("Rollback error:", error);
      await transaction.rollback();
      throw error;
    }
  },
};
