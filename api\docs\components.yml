components:
  schemas:
    Identity:
      type: object
      properties:
        identity_id:
          type: string
        email:
          type: string
          format: email
        username:
          type: string
        first_name:
          type: string
        last_name:
          type: string
      example:
        identity_id: 5ebac534954b54139806c112
        email: <EMAIL>
        username: fake_username
        first_name: Fake
        last_name: Admin

    Token:
      type: object
      properties:
        token:
          type: string
        expires:
          type: string
          format: date-time
      example:
        token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1ZWJhYzUzNDk1NGI1NDEzOTgwNmMxMTIiLCJpYXQiOjE1ODkyOTg0ODQsImV4cCI6MTU4OTMwMDI4NH0.m1U63blB0MLej_WfB7yC2FTMnCziif9X8yzwDEfJXAg
        expires: 2020-05-12T16:18:04.793Z

    AuthTokens:
      type: object
      properties:
        access:
          $ref: "#/components/schemas/Token"
        refresh:
          $ref: "#/components/schemas/Token"

    Facility:
      type: object
      properties:
        facility_id:
          type: string
        name:
          type: string
        facility_code:
          type: string
        facility_type:
          type: number
        time_zone:
          type: string
        phone:
          type: string
        email:
          type: string
          format: email
        address:
          type: object
          properties:
            address_line_1:
              type: string
            country:
              type: string
            state_province:
              type: string
            postal_code:
              type: string
      example:
        facility_id: 609bda561452242d88d36e37
        name: Central Hospital
        facility_code: CH001
        facility_type: 1
        time_zone: UTC-5
        phone: "+**********"
        email: <EMAIL>
        address:
          address_line_1: "123 Main St"
          country: "USA"
          state_province: "California"
          postal_code: "90001"

    Building:
      type: object
      properties:
        building_id:
          type: string
        facility_id:
          type: string
        name:
          type: string
        address:
          type: string
        year_constructed:
          type: integer
        building_code:
          type: string
        status:
          type: number
        type:
          type: string
        occupancy_type:
          type: number
        phone:
          type: string
        email:
          type: string
          format: email
        geo_location_code:
          type: number
          format: float
        other_code:
          type: string
        building_url:
          type: string
        connected_applications:
          type: string
        notes:
          type: string
      example:
        building_id: "64b8f0e2d123e4567890abcd"
        facility_id: "609bda561452242d88d36e37"
        name: "Main Building"
        address: "456 Building Ave"
        year_constructed: 1990
        building_code: "MB001"
        status: 1
        type: 2
        occupancy_type: 0
        phone: "+**********"
        email: "<EMAIL>"
        geo_location_code: 40.712776
        geo_location_long: -74.005974
        other_code: "B001"
        building_url: "http://mainbuilding.com"
        connected_applications: "App1, App2"
        notes: "Some notes"

    Floor:
      type: object
      properties:
        facility_id:
          type: string
        floor_id:
          type: string
        building_id:
          type: string
        floor_number:
          type: integer
        status:
          type: number
        total_square_footage:
          type: number
          format: float
        max_occupancy:
          type: integer
        occupancy_type:
          type: string
      example:
        facility_id: "64c6e1e2d123e4567890efgh"
        floor_id: "64b8f0e2d123e4567890efgh"
        building_id: "64b8f0e2d123e4567890abcd"
        floor_number: 1
        status: 1
        total_square_footage: 2500.50
        max_occupancy: 100
        occupancy_type: "Full"

    Room:
      type: object
      properties:
        facility_id:
          type: string
        room_id:
          type: string
        floor_id:
          type: string
        room_number:
          type: string
        max_occupancy:
          type: integer
        area:
          type: integer
        primary_contact_name:
          type: string
        primary_contact_number:
          type: string
        primary_contact_email:
          type: string
          format: email
        status:
          type: number
      example:
        room_id: "64b8f0e2d123e4567890ijkl"
        facility_id: "64c6e1e2d123e4567890efgh"
        floor_id: "64b8f0e2d123e4567890efgh"
        room_number: "101A"
        max_occupancy: 2
        area: 350
        primary_contact_name: "John Doe"
        primary_contact_number: "**********"
        primary_contact_email: "<EMAIL>"
        status: 0
        
    System:
      type: object
      properties:
        system_id:
          type: string
          description: The unique identifier for the system.
        name:
          type: string
          description: The name of the system.
        system_pacs_id:
          type: integer
          description: The PACS system identifier. Must be a positive integer.
        description:
          type: string
          description: A brief description of the system.
        created_at:
          type: string
          format: date-time
          description: The timestamp when the system was created.
        updated_at:
          type: string
          format: date-time
          description: The timestamp when the system was last updated.
      example:
        system_id: "5ebac534-954b-5413-9806-c11200000001"
        name: "PACS System 1"
        system_pacs_id: 101
        description: "A description of PACS System 1."
        created_at: "2025-02-14T12:00:00Z"
        updated_at: "2025-02-14T12:00:00Z"

    AccessLevel:
      type: object
      properties:
        access_level_id:
          type: string
          description: "Unique identifier for the access level."
        name:
          type: string
          description: "Name of the access level."
        description:
          type: string
          description: "Detailed description of the access level."
        pacs_access_level_id:
          type: string
          description: "External ID from Physical Access Control System."
        system_id:
          type: string
          description: "ID of the associated system."
        status:
          type: number
          description: "Current status of the access level."
        access_level_type:
          type: string
          enum:
            - common
            - datacenter
            - restricted
            - highsecurity
            - visitor
            - emergency
            - classified
            - outdoor
          description: "Type of the access level."
        facility_id:
          type: string
          description: "ID of the associated facility."
        created_at:
          type: string
          format: date-time
          description: "Creation timestamp."
        updated_at:
          type: string
          format: date-time
          description: "Last updated timestamp."
        system:
          type: object
          description: "Associated system details."
          properties:
            system_id:
              type: string
            name:
              type: string
        facility:
          type: object
          description: "Associated facility details."
          properties:
            facility_id:
              type: string
            name:
              type: string
      example:
        access_level_id: "5ebac534-954b-5413-9806-c11200000001"
        name: "Main Entrance"
        description: "Access level for main entrance"
        pacs_access_level_id: "PAC123"
        system_id: "5ebac534-954b-5413-9806-c11200000002"
        status: 1
        access_level_type: "common"
        facility_id: "609bda561452242d88d36e37"
        created_at: "2025-02-14T12:00:00Z"
        updated_at: "2025-02-14T12:00:00Z"
        system:
          system_id: "5ebac534-954b-5413-9806-c11200000002"
          name: "PACS System 1"
        facility:
          facility_id: "609bda561452242d88d36e37"
          name: "Central Hospital"

    FacilityAccessLevel:
      type: object
      properties:
        facility_access_level_id:
          type: string
          description: "Unique identifier for the facility access level."
        access_level_id:
          type: string
          description: "Foreign key referencing the AccessLevel."
        building_id:
          type: string
          description: "Foreign key referencing the Building (optional)."
        floor_id:
          type: string
          description: "Foreign key referencing the Floor (optional)."
        room_id:
          type: string
          description: "Foreign key referencing the Room (optional)."
        entry_restrictions:
          type: string
          description: "Entry restrictions for the access level."
        access_protocol:
          type: string
          description: "Access protocol for the access level."
        created_at:
          type: string
          format: date-time
          description: "Timestamp when the record was created."
        updated_at:
          type: string
          format: date-time
          description: "Timestamp when the record was last updated."
        access_level:
          type: object
          description: "Associated access level details."
          properties:
            access_level_id:
              type: string
            name:
              type: string
        facility:
          type: object
          description: "Associated facility details."
          properties:
            facility_id:
              type: string
            name:
              type: string
        building:
          type: object
          description: "Associated building details."
          properties:
            building_id:
              type: string
            name:
              type: string
        floor:
          type: object
          description: "Associated floor details."
          properties:
            floor_id:
              type: string
            floor_number:
              type: integer
        room:
          type: object
          description: "Associated room details."
          properties:
            room_id:
              type: string
            room_number:
              type: string
      example:
        facility_access_level_id: "d04a73a2-40f0-4d5b-9dce-0ef0a82a5a1f"
        access_level_id: "5ebac534-954b-5413-9806-c11200000001"
        facility_id: "609bda561452242d88d36e37"
        building_id: "64b8f0e2-d123-e456-7890-abcd12345678"
        floor_id: "64b8f0e2-d123-e456-7890-efgh12345678"
        room_id: "64b8f0e2-d123-e456-7890-ijkl12345678"
        entry_restrictions: "No entry after 10pm"
        access_protocol: "Swipe card required"
        created_at: "2025-02-14T12:00:00Z"
        updated_at: "2025-02-14T12:00:00Z"
        access_level:
          access_level_id: "5ebac534-954b-5413-9806-c11200000001"
          name: "Main Entrance"
        facility:
          facility_id: "609bda561452242d88d36e37"
          name: "Central Hospital"
        building:
          building_id: "64b8f0e2-d123-e456-7890-abcd12345678"
          name: "Main Building"
        floor:
          floor_id: "64b8f0e2-d123-e456-7890-efgh12345678"
          floor_number: 1
        room:
          room_id: "64b8f0e2-d123-e456-7890-ijkl12345678"
          room_number: "101A"

    MasterData:
      type: object
      properties:
        master_data_id:
          type: string
        group:
          type: string
        key:
          type: integer
        value:
          type: string
      example:
        master_data_id: "64b8f0e2d123e4567890xyz"
        group: "facility_status"
        key: 0
        value: "Active"
        
    BaseGuest:
      type: object
      properties:
         first_name:
           type: string
           example: John
         last_name:
           type: string
           example: Doe
         email:
           type: string
           format: email
           example: <EMAIL>
         phone:
           type: string
           example: "+**********"
         organization:
           type: string
           example: Health Corp
         guest_type:
           type: integer
           enum: [0, 1, 2]
         relationship_type:
           type: integer
           enum: [0, 1, 2,3]
         relationship_status:
           type: integer
           enum: [0, 1, 2]
         is_emergency_contact:
           type: boolean
         emergency_contact_priority:
           type: integer
           minimum: 1
           maximum: 3
        
 
    GuestCreate:
       allOf:
         - $ref: '#/components/schemas/BaseGuest'
         - type: object
           properties:
             appointment_id:
               type: string
               format: uuid
             start_date:
               type: string
               format: date
             start_time:
               type: string
               format: time
             duration:
               type: integer
             screening:
              type: boolean  
 
    GuestResponse:
       allOf:
         - $ref: '#/components/schemas/BaseGuest'
         - type: object
           properties:
             patient_guest_id:
               type: string
               format: uuid
             image:
               type: string
 
    Error:
      type: object
      properties:
        code:
          type: number
        message:
          type: string

    Language:
      type: object
      properties:
        language_id:
          type: string
        name:
          type: string
        code:
          type: string
        default:
          type: boolean
        status:
          type: boolean
        addons:
          type: object
          description: Additional metadata for the language
        updated_by:
          type: string
          description: ID of the user who last updated the language
      example:
        language_id: "123e4567-e89b-12d3-a456-************"
        name: "English"
        code: "en"
        default: true
        status: true
        addons:
          key1: "value1"
          key2: "value2"
        updated_by: "456e7890-e12b-34d5-a678-90**********"

  responses:
    DuplicateEmail:
      description: Email already taken
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            code: 400
            message: Email already taken
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            code: 401
            message: Please authenticate
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            code: 403
            message: Forbidden
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          example:
            code: 404
            message: Not found

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT


