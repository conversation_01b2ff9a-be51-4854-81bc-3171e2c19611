// CareMate Public Middleware for Tyk Gateway
// Sets dummy headers for public endpoints when API is in Tyk mode

var CareMatePublic = new TykJS.TykMiddleware.NewMiddleware({});

CareMatePublic.NewProcessRequest(function(request, session, config) {
    // Log request for debugging
    log("CareMate Public: Processing request for " + request.RequestURI);
    log("CareMate Public: Method: " + request.Method);
    
    // Get path and method
    var fullPath = request.RequestURI.split('?')[0];
    var method = request.Method.toUpperCase();

    // Strip the listen path prefix to get the actual API path
    var path = fullPath;
    if (fullPath.indexOf('/caremate/api/') === 0) {
        path = fullPath.substring('/caremate/api'.length);
    }

    log("CareMate Public: Full path: " + fullPath + ", Normalized path: " + path);
    
    // For public endpoints that don't require authentication but API expects Tyk headers
    var publicDataEndpoints = ["/countries", "/states", "/timezones", "/languages"];
    var isPublicDataEndpoint = publicDataEndpoints.some(function(endpoint) {
        return path === endpoint;
    });

    // Also handle auth endpoints and health check
    var isAuthEndpoint = path.indexOf('/auth/') === 0;
    var isHealthEndpoint = path === '/health';

    if (isPublicDataEndpoint || isAuthEndpoint || isHealthEndpoint) {
        // Set headers for public endpoints with appropriate permissions
        var publicPermissions = [];

        // Set permissions based on the endpoint
        if (path === '/countries') {
            publicPermissions = ['view_countries'];
        } else if (path === '/states') {
            publicPermissions = ['view_states'];
        } else if (path === '/timezones') {
            publicPermissions = ['view_timezones'];
        } else if (path === '/languages') {
            publicPermissions = ['view_languages'];
        }
        // Auth and health endpoints don't need specific permissions

        request.SetHeaders['x-caremate-identity-id'] = 'public';
        request.SetHeaders['x-caremate-permissions'] = JSON.stringify(publicPermissions);
        request.SetHeaders['x-caremate-auth-method'] = 'public';
        request.SetHeaders['x-caremate-authorized'] = 'true';
        request.SetHeaders['x-caremate-required-permissions'] = '[]';
        request.SetHeaders['x-caremate-endpoint'] = method + ":" + path;

        log("CareMate Public: Set headers for public endpoint: " + path + " with permissions: " + JSON.stringify(publicPermissions));
    } else {
        log("CareMate Public: No special handling needed for: " + path);
    }

    log("CareMate Public: Returning control to Tyk");
    return CareMatePublic.ReturnData(request, {});
});
