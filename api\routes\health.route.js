const { status: httpStatus } = require("http-status");
const express = require("express");
const { sendSuccess, sendError } = require("../helpers/api.helper");
const { sequelize } = require("../models");
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Health
 *   description: Health check API
 * /health:
 *   get:
 *     summary: Health check endpoint.
 *     description: >
 *       This endpoint verifies if the service and its critical dependency (the database)
 *       are operational. It is used by Kubernetes liveness and readiness probes.
 *       A successful check returns a 200 OK status; if the database connection fails,
 *       a 503 Service Unavailable is returned.
 *     responses:
 *       200:
 *         description: Service is healthy and database connection is established.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: OK
 *                 message:
 *                   type: string
 *                   example: Service is healthy
 *       503:
 *         description: Service is unhealthy. Database connection failed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Service is not working
 */
router.get(
  "/",
  async (req, res, next) => {
    await sequelize
      .authenticate()
      .then(() => {
        return sendSuccess(res, "Service is healthy", httpStatus.OK);
      })
      .catch((error) => {
        return sendError(res, error || "Service is not working", httpStatus.SERVICE_UNAVAILABLE);
      });
  }
);

module.exports = router;
