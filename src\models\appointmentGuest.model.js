const history = require("../models/plugins/history.plugin");
const trigger = require("../models/plugins/trigger.plugin");
const rules = require("./rules/appointmentGuest.rules.json");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuest = sequelize.define(
    "AppointmentGuest",
    {
      appointment_guest_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment",
          key: "appointment_id",
        },
        onDelete: "CASCADE",
      },
      patient_guest_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "patient_guest",
          key: "patient_guest_id",
        },
        onDelete: "CASCADE",
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      arrival_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      departure_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      start_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      start_time: {
        type: DataTypes.TIME,
        allowNull: true,
      },
      guest_pin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
      },
      escort_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      screening: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "appointment_guest",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ["appointment_id", "patient_guest_id"],
        },
      ],
    }
  );
  // Ensure 'models' is passed as an argument to this function
  AppointmentGuest.associate = (models) => {

    AppointmentGuest.belongsTo(models.MasterData, {
      foreignKey: "screening",
      targetKey: "key",
      as: "guest_screening_name",
      constraints: false,
      scope: {
        group: "guest_screening",
      },
    });

    AppointmentGuest.belongsTo(models.MasterData, {
      foreignKey: "facility",
      targetKey: "key",
      as: "facility_type_name",
      constraints: false,
      scope: {
        group: "facility_type",
      },
    });
    AppointmentGuest.belongsTo(models.PatientGuest, {
      foreignKey: "patient_guest_id",
      as: "patientGuest",
    });
    AppointmentGuest.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facilityDetails",
    });
  };
  history(AppointmentGuest, sequelize, DataTypes);
  trigger(AppointmentGuest, ["update"], rules);
  return AppointmentGuest;
};
