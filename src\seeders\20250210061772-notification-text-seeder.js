"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('notification_text', [
      {
        notification_text_id: uuidv4(),
        name: 'Send Check In notification',
        template: `We're informing to let you know that you are going to visit your patient, <%= patient_full_name %>`,
        status: 'Active',
        language: 'en-US',
        receiver_column: 'phone',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('notification_text', null, {});
  },
};