const { Country } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");

/**
 * @class CountryController
 * @description Controller for managing countries
 */
const CountryController = {
  index: catchAsync(async (req, res) => {
    const countries = await Country.findAll();
    sendSuccess(res, "Countries retrieved successfully", httpStatus.OK, countries);
  }),

  show: catchAsync(async (req, res) => {
    const { countryId } = req.params;
    const country = await Country.findByPk(countryId);
    if (!country) return sendError(res, "Country not found", httpStatus.NOT_FOUND);
    sendSuccess(res, "Country retrieved successfully", httpStatus.OK, country);
  }),

  create: catchAsync(async (req, res) => {
    const country = await Country.create(req.body);
    sendSuccess(res, "Country created successfully", httpStatus.CREATED, country);
  }),

  update: catchAsync(async (req, res) => {
    const { countryId } = req.params;
    const [updated] = await Country.update(req.body, { where: { country_id: countryId } });
    if (!updated) return sendError(res, "Failed to update country", httpStatus.BAD_REQUEST);
    sendSuccess(res, "Country updated successfully", httpStatus.OK);
  }),

  delete: catchAsync(async (req, res) => {
    const { countryId } = req.params;
    const deleted = await Country.destroy({ where: { country_id: countryId } });
    if (!deleted) return sendError(res, "Failed to delete country", httpStatus.BAD_REQUEST);
    sendSuccess(res, "Country deleted successfully", httpStatus.NO_CONTENT);
  }),
};

module.exports = CountryController;