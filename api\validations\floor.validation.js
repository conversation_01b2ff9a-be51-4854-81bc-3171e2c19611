const Joi = require("joi");
const { exists, unique, existsMasterData } = require("./custom.validation");
const models = require("../models");

// Reusable identifiers
const facilityId = Joi.string()
  .required()
  .external(exists("Facility", "facility_id"));
const building_id = Joi.string().optional().external(exists("Building", "building_id"));
const buildingId = Joi.string().required().external(exists("Building", "building_id"));
const floorId = Joi.string().required().external(exists("Floor", "floor_id"));

// Helper to validate master data keys for floors
const masterDataKey = (group) =>
  Joi.number().integer().external(existsMasterData(group));

const facility = {
  params: Joi.object().keys({
    facilityId,
  }),
};

const building = {
  params: Joi.object().keys({
    buildingId: buildingId,
  }),
};

const create = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object()
    .keys({
      building_id: building_id,
      floor_number: Joi.number().integer().required(),
      total_square_footage: Joi.number().precision(2).optional(),
      max_occupancy: Joi.number().integer().optional(),
      occupancy_type: masterDataKey("floor_occupancy_type").required(),
      status: masterDataKey("floor_status").optional(),
    })
    .external(unique("Floor", ["building_id", "floor_number"])),
};

const floor = {
  params: Joi.object().keys({
    facilityId,
    floorId,
  }),
};

const update = {
  params: Joi.object().keys({
    facilityId,
    floorId,
  }),
  body: Joi.object()
    .keys({
      building_id: building_id,
      floor_number: Joi.number().integer().optional(),
      total_square_footage: Joi.number().precision(2).optional(),
      max_occupancy: Joi.number().integer().optional(),
      occupancy_type: masterDataKey("floor_occupancy_type").optional(),
      status: masterDataKey("floor_status").optional(),
    })
    .min(1)
    .external(async (value, helpers) => {
      const floorId = helpers.state.ancestors[0].params.floorId;
      const currentRecord = await models.Floor.findByPk(floorId);
      if (!currentRecord) return value;
      const data = {
        building_id: value.building_id || currentRecord.building_id,
        floor_number: value.floor_number || currentRecord.floor_number,
      };
      await unique("Floor", ["building_id", "floor_number"], {
        excludeId: floorId,
      })(data, helpers);
      return value;
    }),
};

const status = {
  params: Joi.object().keys({
    facilityId,
    floorId,
  }),
  body: Joi.object().keys({
    status: masterDataKey("floor_status").required(),
  }),
};

module.exports = {
  create,
  floor,
  update,
  status,
  facility,
  building,
};
