const Joi = require('joi');
const { password, unique, exists } = require('./custom.validation');

const register = {
  body: Joi.object().keys({
    username: Jo<PERSON>.string().required().external(unique('Identity', 'username')),
    email: Joi.string().required().email().external(unique('Identity', 'email')),
    password: Joi.string().required().custom(password),
    first_name: Joi.string().max(100),
    last_name: Joi.string().max(100),
  }),
};

const login = {
  body: Joi.object().keys({
    email: Joi.string().required().external(exists('Identity', 'email')),
    password: Joi.string().required(),
  }),
};

const logout = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required(),
  }),
};

const refreshTokens = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required(),
  }),
};

module.exports = {
  register,
  login,
  logout,
  refreshTokens
};
