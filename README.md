# CareMate - Healthcare Management System

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Express.js](https://img.shields.io/badge/Express.js-4.21+-blue.svg)](https://expressjs.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Database-blue.svg)](https://www.postgresql.org/)
[![Tyk](https://img.shields.io/badge/Tyk-API%20Gateway-blue.svg)](https://tyk.io/)
[![RabbitMQ](https://img.shields.io/badge/RabbitMQ-Message%20Queue-orange.svg)](https://www.rabbitmq.com/)

CareMate is a comprehensive healthcare management system built with a microservices architecture. It provides robust patient management, appointment scheduling, visitor management, facility administration, and enterprise-grade scalability with message queuing and API gateway integration.

## 🏗️ System Architecture

CareMate follows a sophisticated microservices architecture with shared components and independent deployments:

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           CAREMATE SYSTEM ARCHITECTURE                             │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TYK GATEWAY   │    │   API PROJECT   │    │   PROCESSOR     │    │   AGENT         │
│   (Optional)    │    │   (Core API)    │    │   (Events)      │    │   (Data Sync)   │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Authentication│───▶│ • REST APIs     │───▶│ • Event Queue   │───▶│ • Data Import   │
│ • Authorization │    │ • Business Logic│    │ • Notifications │    │ • CSV Processing│
│ • Rate Limiting │    │ • Database      │    │ • HL7 Processing│    │ • API Outbound  │
│ • Analytics     │    │ • Rule Engine   │    │ • Email/SMS     │    │ • File Transfer │
│ • SSO Proxy     │    │ • Audit Logging │    │ • Performance   │    │ • Automation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │                        │
        └────────────────────────┼────────────────────────┼────────────────────────┘
                                 │                        │
                    ┌─────────────────────────────────────────────────────┐
                    │              SHARED COMPONENTS                      │
                    ├─────────────────────────────────────────────────────┤
                    │ • Models (50+ Database Models)                     │
                    │ • Config (Database, Auth, Caching, RabbitMQ)       │
                    │ • Services (Auth, Event, Performance, Encryption)  │
                    │ • Helpers (API, Caching, HL7, Global Utilities)    │
                    │ • Migrations & Views (Database Schema)              │
                    │ • Seeders (Master Data, Sample Data)               │
                    └─────────────────────────────────────────────────────┘
                                         │
                                         ▼
                    ┌─────────────────────────────────────────────────────┐
                    │                DATA LAYER                           │
                    ├─────────────────────────────────────────────────────┤
                    │ • PostgreSQL (Primary Database)                    │
                    │ • Redis/Memcached (Caching Layer)                  │
                    │ • RabbitMQ (Message Queue)                         │
                    └─────────────────────────────────────────────────────┘
```

## 📁 Project Structure

```
caremate/
├── 📁 api/                      # Core API Server (Port 3001)
│   ├── controllers/             # Request handlers (25+ controllers)
│   ├── routes/                  # API routes with middleware
│   ├── middlewares/             # Auth, validation, error handling
│   ├── validations/             # Joi schema validation
│   ├── docs/                    # Swagger API documentation
│   ├── app.js                   # Express application setup
│   ├── server.js                # Server entry point
│   └── README.md                # API-specific documentation
├── 📁 processor/                # Event Processing System
│   ├── functions/               # Event processor functions
│   ├── middlewares/             # Internal/external middleware
│   ├── mappings/                # Data transformation configs
│   ├── performances/            # Performance monitoring logs
│   ├── index.js                 # Main processor entry point
│   └── README.md                # Processor-specific documentation
├── 📁 agent/                    # Data Synchronization System
│   ├── handlers/                # Inbound/outbound data handlers
│   ├── mappings/                # Data mapping configurations
│   ├── agent.js                 # Agent automation script
│   ├── index.js                 # Main agent entry point
│   └── README.md                # Agent-specific documentation
├── 📁 tyk/                      # API Gateway Configuration
│   ├── apps/                    # API definitions (public/protected)
│   ├── middleware/              # Gateway middleware functions
│   ├── docker-compose.yml       # Gateway deployment
│   ├── tyk.standalone.conf      # Gateway configuration
│   └── README.md                # Gateway-specific documentation
├── 📁 src/                      # Shared Components (Symlinked)
│   ├── config/                  # Configuration files
│   ├── models/                  # Database models (50+ models)
│   ├── services/                # Business logic services
│   ├── helpers/                 # Utility functions
│   ├── migrations/              # Database migrations
│   ├── seeders/                 # Database seeders
│   └── views/                   # Database views
├── 📁 files/                    # Shared files (copied to projects)
├── 📄 setup.js                  # Symlink setup automation
├── 📄 package.json              # Root dependencies
└── 📄 README.md                 # This comprehensive guide
```

## 🚀 Quick Start

### Prerequisites

Before setting up CareMate, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **npm** (v8 or higher)
- **PostgreSQL** (v12 or higher)
- **Redis** (optional, for caching)
- **RabbitMQ** (optional, for message queuing)
- **Docker & Docker Compose** (for Tyk Gateway)

### 1. Repository Setup

#### Option A: Complete CareMate Setup (Recommended for Development)

**Windows Users: Run Command Prompt as Administrator for this entire process**

```bash
# Clone the main CareMate repository
git clone --branch darshil --single-branch https://git.onetalkhub.com/care/caremate.git
cd caremate

# Install root dependencies
npm install

# Clone individual project repositories inside caremate
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/care-dataflow-processors.git
git clone https://git.onetalkhub.com/care/care-agent.git
git clone https://git.onetalkhub.com/care/tyk.git

# Install dependencies for each project
cd api && npm install && cd ..
cd processor && npm install && cd ..
cd agent && npm install && cd ..
cd tyk && npm install && cd ..

# Create symlinks for shared components
npm run setup

# Clean up any .ignored files that may have been created during symlinking
# Check for .ignored files and remove them manually if found
dir /s *.ignored    # Windows: Check for .ignored files
find . -name "*.ignored"  # Linux/Mac: Check for .ignored files
# If any .ignored files are found, delete them manually
```

#### Option B: Individual Project Setup (For Production/Specific Services)

```bash
# Clone only specific projects you need
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
# ... clone other projects as needed

# Each project contains its own dependencies and configuration
cd api && npm install
```

### 2. Environment Configuration

Create your environment files in the root directory:

```bash
# Copy example environment file
cp .env.local.example .env.local

# Edit environment variables
nano .env.local
```

**Key Environment Variables:**
```env
# Application Mode
NODE_ENV=local
AUTH_MODE=custom  # or 'tyk' for gateway mode

# Database Configuration
DB_WRITE_HOST=localhost
DB_WRITE_USERNAME=your_username
DB_WRITE_PASSWORD=your_password
DB_WRITE_DATABASE=caremate

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_ACCESS_EXPIRATION_MINUTES=300000000

# Optional Services
CACHE_DRIVER=memory  # or 'redis', 'memcached'
MESSAGE_QUEUING=false  # or 'true' for RabbitMQ
```

### 3. Create Symlinks

CareMate uses symbolic links to share common code across projects:

```bash
# Create symlinks for shared components
npm run setup

# Verify symlinks were created
ls -la api/models  # Should show symlinked directories
ls -la processor/config
ls -la agent/services
```

**Windows Users:**
- **Recommended**: Run Command Prompt as Administrator for the entire setup process
- **Alternative**: Enable Developer Mode (Windows 10/11: Settings → Update & Security → For Developers → Developer Mode)
- **Troubleshooting**: If `.ignored` files are created during symlinking, remove them manually after setup

**Important Notes:**
- **Environment Files**: All `.env.*` files from the root directory are automatically symlinked to each project, ensuring consistent configuration across all services
- **Sequelize Configuration**: The `.sequelizerc` file is copied (not symlinked) to each project to maintain database configuration consistency
- **Shared Components**: Models, services, helpers, and config files are symlinked, so changes in one project affect all projects
- **Project-Specific Files**: Files in the `files/` directory are copied to maintain project-specific configurations
- **Symlink Issues**: If symlink creation fails, `.ignored` files may be created - these should be deleted manually

### 4. Database Setup

```bash
# Navigate to API project
cd api

# Install API dependencies
npm install

# Sync database schema
npm run db

# Run migrations (if any)
npx sequelize-cli db:migrate

# Seed initial data
npx sequelize-cli db:seed:all
```

### 5. Start the System

#### Option A: Custom Authentication Mode (Recommended for Development)

```bash
# Start API server
cd api
npm run dev  # Development with auto-reload
# or
npm start    # Production mode

# API will be available at http://localhost:3001
# Swagger docs at http://localhost:3001/docs
```

#### Option B: Tyk Gateway Mode (Enterprise)

```bash
# 1. Start Tyk Gateway
cd tyk
docker-compose up -d

# 2. Configure API for Tyk mode
cd ../api
# Edit .env.local: AUTH_MODE=tyk
npm start

# 3. Access via Gateway
# Public API: http://localhost:8181/caremate/api/
# Protected API: http://localhost:8181/caremate/protected/
```

### 6. Optional: Start Additional Services

```bash
# Start Processor (for event processing)
cd processor
npm install
node index.js --queue=notification_queue

# Start Agent (for data synchronization)
cd agent
npm install
node index.js --agent local_connection_batch_100
```

## 🔧 Project Components

### 1. API Project - Core Healthcare API

The API project is the heart of the CareMate system, providing comprehensive healthcare management capabilities.

**Key Features:**
- **Patient Management**: Comprehensive patient records and history
- **Appointment Scheduling**: Advanced appointment booking system
- **Visitor Management**: Guest registration and tracking
- **Facility Management**: Multi-facility support with access levels
- **Dual Authentication**: Custom JWT + Tyk Gateway support
- **Rule Engine**: Dynamic business rules with event generation
- **HIPAA Compliance**: Comprehensive audit logging

**Technology Stack:**
- Express.js 4.21+ (Web framework)
- Sequelize 6.37+ (ORM)
- PostgreSQL (Database)
- JWT 9.0+ (Authentication)
- Swagger (API documentation)

**Quick Commands:**
```bash
cd api
npm run dev          # Development server
npm run db           # Sync database
npm run db:refresh   # Reset database
npm test             # Run tests
```

### 2. Processor Project - Event Processing System

The Processor handles asynchronous event processing, notifications, and data transformations.

**Key Features:**
- **Event-Driven Architecture**: RabbitMQ-based message processing
- **Multi-Channel Notifications**: Email, SMS, push notifications
- **HL7 Processing**: Healthcare data integration
- **Performance Monitoring**: Comprehensive metrics and logging
- **Dynamic Configuration**: Database-driven processor configuration

**Available Processors:**
- **HL7 Processor**: Healthcare message processing
- **Notification Generator**: Multi-channel notification creation
- **Email Sender**: SMTP-based email delivery
- **SMS Sender**: Telnyx API integration
- **Data Parser**: External data processing

**Quick Commands:**
```bash
cd processor
node index.js --queue=notification_queue    # Start notification processor
node index.js --queue=hl7_queue            # Start HL7 processor
node index.js --queue=email_queue          # Start email processor
```

### 3. Agent Project - Data Synchronization

The Agent system handles data import/export and external system integration.

**Key Features:**
- **Inbound Agents**: FTP, S3, Azure, Local file processing
- **Outbound Agents**: CSV generation, API integration
- **Data Transformation**: Flexible mapping configurations
- **Batch Processing**: Efficient bulk data operations
- **Performance Monitoring**: Specialized agent metrics

**Agent Types:**
- **Local Connection**: Local directory monitoring
- **FTP Connection**: FTP server integration
- **S3 Connection**: AWS S3 bucket processing
- **Azure Connection**: Azure Blob storage
- **API Outbound**: REST API data transmission

**Quick Commands:**
```bash
cd agent
node index.js --agent local_connection_batch_100  # Local file agent
node index.js --agent ftp_connection_main         # FTP agent
node index.js --agent api_2_outbound_external     # API outbound
```

### 4. Tyk Gateway - API Management

The Tyk Gateway provides enterprise-grade API management capabilities.

**Key Features:**
- **Authentication & Authorization**: JWT-based security
- **Rate Limiting**: Configurable API throttling
- **Analytics**: Comprehensive API usage metrics
- **SSO Integration**: SAML, OpenID Connect, Azure AD
- **Dual API Setup**: Public and protected endpoints

**Gateway Configuration:**
- **Public API** (`/caremate/api/`): Authentication, health checks, master data
- **Protected API** (`/caremate/protected/`): Business logic endpoints

**Quick Commands:**
```bash
cd tyk
docker-compose up -d                    # Start gateway
node test-caremate-auth.js             # Test authentication
curl http://localhost:8181/hello       # Health check
```

## 📊 Database Diagram

You can view the database diagram using the link below:

🔗 [View DB Diagram](./db_diagram.svg)

Or open the raw HTML version directly in your browser:

🌐 [Open Raw DB Diagram](https://git.onetalkhub.com/care/caremate/-/blob/darshil/db_diagram.svg)

## 🔗 Shared Components Architecture

CareMate uses a sophisticated shared component system to maintain consistency and reduce code duplication across all projects.

### Symlink System

The `setup.js` script creates symbolic links to share common code:

```javascript
// Projects that receive shared components
const projectNames = ["api", "processor", "agent"];

// Shared directories (symlinked)
src/config/     → api/config/, processor/config/, agent/config/
src/models/     → api/models/, processor/models/, agent/models/
src/services/   → api/services/, processor/services/, agent/services/
src/helpers/    → api/helpers/, processor/helpers/, agent/helpers/
src/migrations/ → api/migrations/, processor/migrations/, agent/migrations/
src/seeders/    → api/seeders/, processor/seeders/, agent/seeders/
src/views/      → api/views/, processor/views/, agent/views/

// Environment files (symlinked)
.env.local      → api/.env.local, processor/.env.local, agent/.env.local
.env.dev        → api/.env.dev, processor/.env.dev, agent/.env.dev
.env.staging    → api/.env.staging, processor/.env.staging, agent/.env.staging
.env.prod       → api/.env.prod, processor/.env.prod, agent/.env.prod

// Configuration files (copied)
files/.sequelizerc → api/.sequelizerc, processor/.sequelizerc, agent/.sequelizerc
files/ecosystem.config.js → api/ecosystem.config.js, processor/ecosystem.config.js, agent/ecosystem.config.js
files/*         → api/*, processor/*, agent/* (other static files)
```

### Shared Components Overview

#### 1. **Models** (50+ Database Models)
- **Patient Models**: Patient, PatientGuest, PatientIdentifier
- **Appointment Models**: Appointment, AppointmentGuest, AppointmentGuestScreening
- **Facility Models**: Facility, Building, Floor, Room
- **Identity Models**: Identity, IdentityAccess, IdentityRole
- **Event Models**: Event, EventTrace, EventAction
- **Notification Models**: Notification, NotificationEmail, NotificationText

#### 2. **Configuration**
- **Database**: Read/write splitting, connection pooling
- **Authentication**: Passport.js strategies, JWT configuration
- **Caching**: Redis, Memcached, memory caching
- **Message Queue**: RabbitMQ configuration
- **Logging**: Winston logger with daily rotation

#### 3. **Services**
- **Auth Service**: Authentication and authorization logic
- **Event Service**: Event processing and RabbitMQ operations
- **Performance Service**: Metrics collection and monitoring
- **Encryption Service**: Data encryption and security
- **CSV Service**: Data export and import utilities

#### 4. **Helpers**
- **API Helper**: Common API utilities and responses
- **Caching Helper**: Multi-level caching operations
- **HL7 Helper**: Healthcare data parsing and transformation
- **Global Helper**: Shared utility functions

### Benefits of Shared Architecture

```
SHARED COMPONENT BENEFITS:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ 🔄 CONSISTENCY:                                                                    │
│ • Single source of truth for business logic                                        │
│ • Consistent database models across all projects                                   │
│ • Unified configuration management                                                 │
│ • Standardized error handling and logging                                          │
│                                                                                     │
│ 🚀 DEVELOPMENT EFFICIENCY:                                                          │
│ • No code duplication across projects                                              │
│ • Faster development with shared utilities                                         │
│ • Easy maintenance and updates                                                     │
│ • Consistent API responses and patterns                                            │
│                                                                                     │
│ 📦 DEPLOYMENT FLEXIBILITY:                                                          │
│ • Independent project deployments                                                  │
│ • Separate repositories possible                                                   │
│ • Scalable microservices architecture                                              │
│ • Environment-specific configurations                                              │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 🔧 Setup and Management Commands

### Symlink Management

```bash
# Create all symlinks and copy files
npm run setup

# Remove all symlinks (cleanup)
npm run setup:unlink

# Setup with specific environment
npm run setup -- --env-file .env.dev
```

#### What happens during `npm run setup`:

**1. Shared Directories (Symlinked):**
- `src/config/` → Creates symlinks in `api/config/`, `processor/config/`, `agent/config/`
- `src/models/` → Creates symlinks in `api/models/`, `processor/models/`, `agent/models/`
- `src/services/` → Creates symlinks in `api/services/`, `processor/services/`, `agent/services/`
- `src/helpers/` → Creates symlinks in `api/helpers/`, `processor/helpers/`, `agent/helpers/`
- `src/migrations/` → Creates symlinks in `api/migrations/`, `processor/migrations/`, `agent/migrations/`
- `src/seeders/` → Creates symlinks in `api/seeders/`, `processor/seeders/`, `agent/seeders/`
- `src/views/` → Creates symlinks in `api/views/`, `processor/views/`, `agent/views/`

**2. Environment Files (Symlinked):**
- `.env.local` → Creates symlinks in `api/.env.local`, `processor/.env.local`, `agent/.env.local`
- `.env.dev` → Creates symlinks in `api/.env.dev`, `processor/.env.dev`, `agent/.env.dev`
- `.env.staging` → Creates symlinks in `api/.env.staging`, `processor/.env.staging`, `agent/.env.staging`
- `.env.prod` → Creates symlinks in `api/.env.prod`, `processor/.env.prod`, `agent/.env.prod`
- All `.env.*` files from root are automatically symlinked to each project

**3. Configuration Files (Copied):**
- `files/.sequelizerc` → Copied to `api/.sequelizerc`, `processor/.sequelizerc`, `agent/.sequelizerc`
- `files/ecosystem.config.js` → Copied to each project for PM2 deployment
- Other files in `files/` directory are copied to maintain project-specific configurations

**4. Smart Link Management:**
- Existing symlinks pointing to correct locations are skipped
- Existing symlinks pointing to wrong locations are replaced
- Regular files at destination are deleted before creating symlinks
- Backup of existing files is not created (files are permanently deleted)

### Database Management

```bash
# Sync database schema (safe)
cd api && npm run db

# Sync with specific environment
cd api && npm run db -- --env-file .env.dev

# Refresh database (drops and recreates)
cd api && npm run db:refresh

# Reset specific models
cd api && npm run db:refresh -- --model Facility Floor Room
```

### Migration and Seeding

```bash
# Run migrations
cd api && npx sequelize-cli db:migrate

# Seed all data
cd api && npx sequelize-cli db:seed:all

# Seed specific seeder
cd api && npx sequelize-cli db:seed --seed 20250210061756-seed-permissions-admin-role.js

# Undo seeding
cd api && npx sequelize-cli db:seed:undo:all
```

### Testing Utilities

```bash
# Generate test CSV data
npm run csv        # Default size
npm run csv:1000   # 1000 records
npm run csv:5000   # 5000 records
npm run csv:10000  # 10000 records
```

## 🔐 Authentication Architecture

CareMate implements a sophisticated dual authentication system supporting both custom JWT and enterprise API gateway authentication.

### Authentication Modes

#### Custom Mode (`AUTH_MODE=custom`)
- Direct JWT-based authentication
- Internal user management
- Suitable for development and standalone deployments
- Complete control over authentication flow

#### Tyk Gateway Mode (`AUTH_MODE=tyk`)
- API Gateway handles authentication
- Enterprise-grade features (rate limiting, analytics)
- Centralized authentication across microservices
- Suitable for production enterprise deployments

### Authentication Flow Diagram

```
CUSTOM MODE FLOW:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Client      │───▶│ API Server  │───▶│ JWT         │───▶│ Business    │
│ Request     │    │ Direct      │    │ Validation  │    │ Logic       │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘

TYK GATEWAY MODE FLOW:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Client      │───▶│ Tyk Gateway │───▶│ JWT         │───▶│ API Server  │───▶│ Business    │
│ Request     │    │             │    │ Validation  │    │ (Headers)   │    │ Logic       │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### SSO Integration

CareMate supports multiple SSO providers through Passport.js:

- **SAML 2.0**: Enterprise identity provider integration
- **OpenID Connect**: Modern OAuth2/OIDC flow
- **Azure AD**: Native Microsoft integration
- **Custom Strategies**: Extensible authentication framework

## 📊 Performance and Monitoring

### Performance Monitoring Features

- **Session-Based Tracking**: Unique performance tracking per operation
- **Metric Collection**: Processing times, throughput, success rates
- **File-Based Logging**: Timestamped performance logs
- **Real-Time Metrics**: Live performance insights

### Caching Strategy

```
MULTI-LEVEL CACHING ARCHITECTURE:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           CACHING LAYERS                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ L1: Memory Cache (Fastest)                                                         │
│ • Function mappings (10 hours TTL)                                                │
│ • Event configurations (10 hours TTL)                                             │
│ • Application metadata (24 hours TTL)                                             │
│                                                                                     │
│ L2: Redis/Memcached (Fast)                                                         │
│ • Master data (1 hour TTL)                                                        │
│ • Notification templates (1 hour TTL)                                             │
│ • User sessions and permissions                                                    │
│                                                                                     │
│ L3: Database Views (Optimized)                                                     │
│ • Pre-computed complex queries                                                     │
│ • Aggregated reporting data                                                        │
│ • Historical audit information                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 🚀 Deployment Strategies

### Development Deployment

```bash
# Single machine development
cd api && npm run dev     # API server with auto-reload
cd processor && node index.js --queue=notification_queue  # Event processing
cd agent && node index.js --agent local_connection        # Data sync
```

### Production Deployment

#### Using PM2 (Recommended)

```bash
# API Server
cd api
pm2 start server.js --name="caremate-api" --env-file=.env.prod

# Processor Services
cd processor
pm2 start ecosystem.config.js --only notification_processor
pm2 start ecosystem.config.js --only hl7_processor
pm2 start ecosystem.config.js --only email_processor

# Agent Services
cd agent
pm2 start ecosystem.config.js --only local_agent
pm2 start ecosystem.config.js --only ftp_agent
```

#### Using Docker

```bash
# Build and run API
cd api
docker build -t caremate-api .
docker run -d --name caremate-api -p 3001:3001 --env-file .env.prod caremate-api

# Tyk Gateway
cd tyk
docker-compose up -d
```

### Independent Repository Deployment

CareMate projects can be deployed as separate repositories:

```bash
# Split into separate repositories
git subtree push --prefix=api origin api-main
git subtree push --prefix=processor origin processor-main
git subtree push --prefix=agent origin agent-main
git subtree push --prefix=tyk origin tyk-main

# Deploy independently
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
git clone https://git.onetalkhub.com/care/agent.git
git clone https://git.onetalkhub.com/care/tyk.git
```

## � Repository Management & Deployment Workflow

CareMate supports both monorepo and individual repository approaches for maximum deployment flexibility.

### Development Workflow

#### 1. Initial Setup (Monorepo Approach)

```bash
# Step 1: Clone main CareMate repository
git clone https://git.onetalkhub.com/care/caremate.git
cd caremate

# Step 2: Install root dependencies
npm install

# Step 3: Clone individual project repositories
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
git clone https://git.onetalkhub.com/care/agent.git
git clone https://git.onetalkhub.com/care/tyk.git

# Step 4: Install dependencies for each project
for project in api processor agent tyk; do
  echo "Installing dependencies for $project..."
  cd $project && npm install && cd ..
done

# Step 5: Create symlinks for shared components
npm run setup

# Step 6: Verify setup
ls -la api/models     # Should show symlinked directories
ls -la processor/config
ls -la agent/services
ls -la api/.env.local # Should show symlinked environment files
ls -la api/.sequelizerc # Should show copied configuration files
```

#### 2. Development Process

```bash
# Work on shared components (affects all projects)
# Edit files in src/ directory
nano src/models/patient.model.js
nano src/config/database.js

# Work on project-specific features
cd api
# Make changes to API-specific files
nano controllers/patient.controller.js
nano routes/patient.route.js

# Test changes
npm run dev
```

#### 3. Pushing Updates to Individual Repositories

```bash
# Push updates to individual project repositories
cd api
git add .
git commit -m "feat: add new patient management features"
git push origin main

cd ../processor
git add .
git commit -m "feat: add new notification processor"
git push origin main

cd ../agent
git add .
git commit -m "feat: add S3 integration agent"
git push origin main

cd ../tyk
git add .
git commit -m "feat: update gateway configuration"
git push origin main

# Push updates to main CareMate repository (shared components)
cd ..
git add src/
git commit -m "feat: update shared models and services"
git push origin main
```

### Production Deployment Strategies

#### Strategy 1: Independent Service Deployment

```bash
# Deploy each service independently from their own repositories

# API Service Deployment
git clone https://git.onetalkhub.com/care/api.git
cd api
npm install
npm run db
npm start

# Processor Service Deployment
git clone https://git.onetalkhub.com/care/processor.git
cd processor
npm install
node index.js --queue=notification_queue

# Agent Service Deployment
git clone https://git.onetalkhub.com/care/agent.git
cd agent
npm install
node index.js --agent local_connection_batch_100

# Tyk Gateway Deployment
git clone https://git.onetalkhub.com/care/tyk.git
cd tyk
docker-compose up -d
```

#### Strategy 2: Coordinated Deployment (Using Main Repository)

```bash
# Deploy all services from main repository
git clone https://git.onetalkhub.com/care/caremate.git
cd caremate

# Pull latest individual projects
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
git clone https://git.onetalkhub.com/care/agent.git
git clone https://git.onetalkhub.com/care/tyk.git

# Setup and deploy
npm install
npm run setup

# Deploy services
cd api && npm install && npm start &
cd ../processor && npm install && node index.js --queue=notification_queue &
cd ../agent && npm install && node index.js --agent local_connection_batch_100 &
cd ../tyk && docker-compose up -d
```

### Environment-Specific Deployments

#### Development Environment

```bash
# Use main repository for development
git clone https://git.onetalkhub.com/care/caremate.git
cd caremate

# Clone all projects for full development environment
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
git clone https://git.onetalkhub.com/care/agent.git
git clone https://git.onetalkhub.com/care/tyk.git

npm install && npm run setup

# Start development servers
cd api && npm run dev &
cd ../processor && node index.js --queue=notification_queue &
```

#### Staging Environment

```bash
# Use individual repositories for staging
mkdir caremate-staging && cd caremate-staging

git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
git clone https://git.onetalkhub.com/care/tyk.git

# Deploy with staging configurations
cd api && npm install && npm start --env-file=.env.staging &
cd ../processor && npm install && node index.js --queue=notification_queue &
cd ../tyk && docker-compose -f docker-compose.staging.yml up -d
```

#### Production Environment

```bash
# Use individual repositories for production
mkdir caremate-production && cd caremate-production

# Clone only required services
git clone https://git.onetalkhub.com/care/api.git
git clone https://git.onetalkhub.com/care/processor.git
git clone https://git.onetalkhub.com/care/tyk.git

# Deploy with PM2 for production
cd api && npm install && pm2 start ecosystem.config.js --env production
cd ../processor && npm install && pm2 start ecosystem.config.js --env production
cd ../tyk && docker-compose -f docker-compose.prod.yml up -d
```

### Benefits of This Approach

```
REPOSITORY MANAGEMENT BENEFITS:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ 🔄 DEVELOPMENT FLEXIBILITY:                                                        │
│ • Monorepo for shared component development                                         │
│ • Individual repos for service-specific features                                   │
│ • Easy switching between development approaches                                     │
│ • Consistent shared components across all services                                 │
│                                                                                     │
│ 🚀 DEPLOYMENT OPTIONS:                                                              │
│ • Independent service deployments                                                  │
│ • Coordinated full-stack deployments                                               │
│ • Environment-specific configurations                                              │
│ • Scalable microservices architecture                                              │
│                                                                                     │
│ 🔧 MAINTENANCE ADVANTAGES:                                                          │
│ • Automated symlink management                                                     │
│ • Synchronized shared components                                                   │
│ • Version control for individual services                                          │
│ • Easy rollback and version management                                             │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## �🔒 Security and Compliance

### HIPAA Compliance Features

- **Audit Logging**: Complete activity tracking with trace IDs
- **Data Encryption**: Sensitive data encryption at rest and in transit
- **Access Controls**: Role-based permissions with fine-grained control
- **Secure Communication**: HTTPS enforcement and secure headers

### Security Best Practices

- **JWT Token Management**: Secure token generation and validation
- **Rate Limiting**: API protection against abuse
- **Input Validation**: Comprehensive request validation using Joi
- **SQL Injection Protection**: Sequelize ORM protection
- **CORS Configuration**: Proper cross-origin request handling

## 📚 API Documentation

### Swagger Documentation

Access interactive API documentation:
- **Local Development**: [http://localhost:3001/docs](http://localhost:3001/docs)
- **Tyk Gateway**: [http://localhost:8181/caremate/api/docs](http://localhost:8181/caremate/api/docs)

### Test Credentials

For testing and development:
```
Admin User:
Email: <EMAIL>
Password: Pa$w0rd!

Kiosk User:
Email: <EMAIL>
Password: Pa$w0rd!
```

## 🛠️ Development Guidelines

### Code Quality Standards

- **ESLint**: JavaScript linting with healthcare-specific rules
- **Prettier**: Consistent code formatting
- **Naming Conventions**: Clear, descriptive naming patterns
- **Documentation**: Comprehensive JSDoc comments
- **Testing**: Unit and integration tests for critical functionality

### Database Conventions

- **Table Names**: Singular form (e.g., `patient`, not `patients`)
- **Column Names**: snake_case format
- **Primary Keys**: `{table_name}_id` pattern
- **Foreign Keys**: Reference target table's primary key name

## 🔧 Troubleshooting

### Common Issues

1. **Symlink Creation Fails**:
   ```bash
   # Windows: Run as Administrator or enable Developer Mode
   # Linux/Mac: Check file permissions
   ls -la src/  # Verify source directories exist
   ```

2. **Database Connection Issues**:
   ```bash
   # Check environment variables
   cd api && node -e "console.log(require('./config/database.js'))"

   # Test database connection
   cd api && npm run db -- --test-connection
   ```

3. **Port Conflicts**:
   ```bash
   # Check if ports are in use
   netstat -an | grep 3001  # API port
   netstat -an | grep 8181  # Tyk Gateway port
   ```

4. **RabbitMQ Connection Issues**:
   ```bash
   # Check RabbitMQ status
   docker ps | grep rabbitmq

   # View processor logs
   cd processor && tail -f logs/processor.log
   ```

### Logs and Debugging

```bash
# API Server logs
cd api && tail -f logs/app.log

# Processor logs
cd processor && tail -f logs/processor.log

# Agent logs
cd agent && tail -f logs/agent.log

# Tyk Gateway logs
cd tyk && docker-compose logs -f gateway
```

## 🌟 Key Features Summary

### Healthcare Management
- **Patient Records**: Comprehensive patient management with history tracking
- **Appointment System**: Advanced scheduling with guest management
- **Visitor Management**: Complete guest registration and screening
- **Facility Administration**: Multi-facility support with access controls

### Enterprise Architecture
- **Microservices Design**: Independent, scalable service components
- **Shared Components**: Consistent code sharing across projects
- **Event-Driven Processing**: Asynchronous event handling with RabbitMQ
- **API Gateway Integration**: Enterprise-grade API management with Tyk

### Data Integration
- **HL7 Processing**: Healthcare data standard integration
- **Multi-Source Agents**: FTP, S3, Azure, local file processing
- **API Outbound**: REST API integration for external systems
- **Data Transformation**: Flexible mapping and transformation engine

### Security & Compliance
- **Dual Authentication**: Custom JWT and API Gateway modes
- **HIPAA Compliance**: Comprehensive audit logging and data protection
- **Role-Based Access**: Fine-grained permission system
- **Multi-Provider SSO**: SAML, OpenID Connect, Azure AD integration

## 🚀 Why Choose CareMate?

### For Healthcare Organizations
- **HIPAA Compliant**: Built with healthcare regulations in mind
- **Scalable Architecture**: Grows with your organization
- **Integration Ready**: Connects with existing healthcare systems
- **Comprehensive Features**: All-in-one healthcare management solution

### For Development Teams
- **Modern Technology Stack**: Latest Node.js, Express, PostgreSQL
- **Clean Architecture**: Well-structured, maintainable codebase
- **Extensive Documentation**: Comprehensive guides and API docs
- **Flexible Deployment**: Multiple deployment options and configurations

### For IT Operations
- **Enterprise Ready**: Production-grade monitoring and logging
- **High Availability**: Scalable microservices architecture
- **Security First**: Built-in security features and compliance
- **Easy Maintenance**: Automated setup and management tools

## 📄 License

This project is proprietary software. All rights reserved.

---

**CareMate Healthcare Management System**
*Built with ❤️ by the OneTalkHub Team*

For detailed information about individual components, refer to their respective README files:
- [API Documentation](./api/README.md) - Core healthcare API with comprehensive features
- [Processor Documentation](./processor/README.md) - Event processing and notification system
- [Agent Documentation](./agent/README.md) - Data synchronization and integration
- [Tyk Gateway Documentation](./tyk/README.md) - API gateway configuration and management