const Joi = require('joi');
const { exists } = require('./custom.validation');

/**
 * Schema for creating a new system.
 * No external validations are needed for uniqueness here since
 */
const create = {
  body: Joi.object().keys({
    name: Joi.string().max(100).required().label("Name"),
    system_pacs_id: Joi.number()
      .integer()
      .min(1)
      .required()
      .label("System PACS ID"),
    description: Joi.string().optional().allow(null, '').label("Description"),
  }),
};

/**
 * Schema for updating an existing system.
 * This uses the custom external `exists` validator to ensure that the system exists.
 */
const update = {
  params: Joi.object().keys({
    systemId: Joi.string()
      .guid({ version: ['uuidv4'] })
      .required()
      .external(exists('System', 'system_id'))
      .label("System ID"),
  }),
  body: Joi.object().keys({
    name: Joi.string().max(100).optional().label("Name"),
    system_pacs_id: Joi.number()
      .integer()
      .min(1)
      .optional()
      .label("System PACS ID"),
    description: Joi.string().optional().allow(null, '').label("Description"),
  }).min(1),
};

/**
 * Schema for retrieving a system by ID.
 * Also uses the custom external `exists` validator.
 */
const system = {
  params: Joi.object().keys({
    systemId: Joi.string()
      .guid({ version: ['uuidv4'] })
      .required()
      .external(exists('System', 'system_id'))
      .label("System ID"),
  }),
};

module.exports = {
  create,
  update,
  system,
};
