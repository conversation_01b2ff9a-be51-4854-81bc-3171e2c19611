'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_identity_access AS
      SELECT
        ia.identity_access_id,
        al.name,
        ia.status,
        al.pacs_area_name, 
        al.online,
        al.requestable_self_service,
        al.access_level_type,
        f.name AS facility_name,
        s.name AS system_name,
        c.template AS card_type
      FROM identity_access ia
      LEFT JOIN access_level al ON ia.identity_access_id = al.access_level_id
      LEFT JOIN facility f ON al.facility_id = f.facility_id
      LEFT JOIN system s ON al.system_id = s.system_id
      LEFT JOIN card c ON ia.card_id = c.card_id;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS view_identity_access;
    `);
  },
};
