const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const NotificationEmail = sequelize.define(
    "NotificationEmail",
    {
      notification_email_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      subject: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      template: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      receiver_column: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      language: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "notification_email",
      timestamps: true,
      underscored: true,
    }
  );

  history(NotificationEmail, sequelize, DataTypes);

  return NotificationEmail;
};