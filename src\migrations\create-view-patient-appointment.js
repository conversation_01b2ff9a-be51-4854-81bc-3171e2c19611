'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
    CREATE VIEW view_patient_appointment AS
       SELECT
        a.appointment_id,
        a.patient_id,
        a.appointment_date,
        a.department,
        a.provider_name,
        a.arrival_time,
        a.departure_time,
        a.status AS appointment_status,
        ms_status.value AS appointment_status_name,
        a.type,
        ms_type.value AS appointment_type_name,
        a.facility_id,
        a.beds,  
        fl.floor_number AS floor_number, 
        b.name AS building_name,           
        r.room_number AS room_number, 
        a.updated_by,
        p.first_name,
        p.last_name,
        p.middle_name,
        p.preferred_name,
        p.email,
        p.phone,
        p.confidentiality_code,
        (p.first_name || ' ' || p.last_name) AS patient_name,
        COALESCE(a.screening, false) AS screening, 
        pi.identifier_value AS mrn,
        f.name AS facility_name,
        p.birth_date,
        p.death_date,
        p.gender AS patient_gender,
        p.image AS image,
        pa.address_line_1,
        pa.address_line_2,
        pa.city,
        c.name AS country,
        s.name AS state,
        pa.postal_code
      FROM appointment a
      LEFT JOIN patient p ON a.patient_id = p.patient_id
      LEFT JOIN patient_identifier pi ON pi.patient_id = p.patient_id
      LEFT JOIN facility f ON a.facility_id = f.facility_id
      LEFT JOIN floor fl ON fl.facility_id = a.facility_id 
      LEFT JOIN building b ON b.facility_id = a.facility_id  
      LEFT JOIN room r ON r.facility_id = a.facility_id
      LEFT JOIN patient_address pa ON pa.patient_id = p.patient_id
      LEFT JOIN country c ON pa.country_id = c.country_id
      LEFT JOIN state s ON pa.state_id = s.state_id
      LEFT JOIN master_data ms_status
        ON ms_status.key = a.status
        AND ms_status.group = 'appointment_status'
      LEFT JOIN master_data ms_type
        ON ms_type.key = a.type
        AND ms_type.group = 'appointment_type';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS view_patient_appointment;`
    );
  },
};