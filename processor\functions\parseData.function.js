const models = require('../models');
const logger = require('../config/logger');
const { markRecordsAsProcessed, markRecordsAsFailed } = require('../../agent/services/staging.service');
const { Op } = require('sequelize');

/**
 * Maps CSV data to model fields based on mapping configuration
 * @param {Object} data - The CSV row data
 * @param {Object} mappingConfig - The mapping configuration object
 * @param {String} mappingType - Either "columnName" or "columnIndex"
 * @param {String} modelName - The target model name
 * @returns {Object} - Mapped data for the specified model
 */
const mapDataToModelFields = (data, mappingConfig, mappingType = 'columnName', modelName) => {
  const mappedData = {};
  const mappings = mappingType === 'columnName' ? mappingConfig.mappings : mappingConfig.columnIndex;

  for (const [key, value] of Object.entries(data)) {
    const fieldPath = mappings[key];
    if (fieldPath && fieldPath.startsWith(`${modelName}.`)) {
      const field = fieldPath.split('.')[1];
      // Convert empty strings to null
      mappedData[field] = value === "" ? null : value;
    }
  }

  return mappedData;
};

/**
 * Validates required fields and data types
 * @param {Object} data - The mapped data
 * @param {Object} mappingConfig - The mapping configuration object
 * @returns {Object} - Validation result with isValid flag and errors
 */
const validateData = (data, mappingConfig) => {
  const errors = [];
  const { validation } = mappingConfig;

  // Check required fields
  for (const [field, rules] of Object.entries(validation)) {
    const mappedField = mappingConfig.mappings[field].split('.')[1];

    if (rules.required && !data[mappedField]) {
      errors.push(`${field} is required`);
    }

    // Basic type validation
    if (data[mappedField] && rules.type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data[mappedField])) {
        errors.push(`${field} must be a valid email address`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Process data from CSV and insert/update in database
 * @param {Object} eventInstance - The event instance containing data
 * @param {Object} context - Execution context with function information
 * @returns {Promise<Object>} Results of the processing operation
 */
const parseData = async (eventInstance, context) => {
  const traceId = context.trace_id;
  const messageIndex = context.messageIndex || 'unknown';
  const collectionId = context.collectionId || 'unknown';

  logger.info(`[TRACE ${traceId}] Starting data parsing and processing (Collection: ${collectionId}, Message: ${messageIndex})`);

  // Use collection performance monitor if available, otherwise create individual monitor
  let performanceMonitor = null;
  if (context.collectionPerformanceMonitor) {
    performanceMonitor = context.collectionPerformanceMonitor;
    logger.info(`[TRACE ${traceId}] Using collection performance monitor for detailed logging`);
  }
  if (performanceMonitor)
    performanceMonitor.startStep(`ParseData Function - Message ${messageIndex}`, {
      traceId,
      messageIndex,
      collectionId,
      functionName: 'parseData'
    });

  try {
    const { batch, agent } = eventInstance;
    const functionId = context.function.function_id;

    // Pass function_id to context for bulk operations - EventTrace will be created in hooks
    context.bulkOperation = batch && batch.length > 1;
    context.functionId = functionId;
    if (performanceMonitor)
      performanceMonitor.endStep(`ParseData Function - Message ${messageIndex}`, {
        batchSize: batch?.length || 0,
        agentId: agent?.agent_id || 'unknown',
        bulkOperation: context.bulkOperation
      });

    // Step 1: Configuration Loading and Validation
    if (performanceMonitor)
      performanceMonitor.startStep(`Configuration Loading - Message ${messageIndex}`, {
        agentMapping: agent?.mapping,
        stagingKey: agent?.stagging_key
      });

    // Get mapping configuration from agent
    let mappingName = agent?.mapping;
    if (!mappingName) {
      logger.error(`[TRACE ${traceId}] No mapping configuration specified in agent`);
      throw new Error('No mapping configuration specified in agent');
    }

    // Get staging key from agent
    const stagingKey = agent?.stagging_key;
    if (!stagingKey) {
      logger.error(`[TRACE ${traceId}] No staging key specified in agent`);
      throw new Error('No staging key specified in agent');
    }

    // Dynamically import mapping configuration
    const mappingConfig = require(`../mappings/${mappingName}.mapping.json`);

    // Extract model name from mapping configuration
    const firstMapping = Object.values(mappingConfig.mappings)[0];
    const modelName = firstMapping.split('.')[0];

    // Get the model dynamically
    const Model = models[modelName];
    if (!Model) {
      logger.error(`[TRACE ${traceId}] Model ${modelName} not found`);
      throw new Error(`Model ${modelName} not found`);
    }
    if (performanceMonitor)
      performanceMonitor.endStep(`Configuration Loading - Message ${messageIndex}`, {
        mappingName,
        modelName,
        stagingKey,
        mappingType: mappingConfig.mappingType
      });

    logger.info(`[TRACE ${traceId}] Processing ${batch.length} records for model ${modelName} using mapping ${mappingName}`);

    const results = {
      success: 0,
      failed: 0,
      errors: [],
      successfulRecords: [],
      failedRecords: []
    };

    // Step 2: Data Mapping and Validation
    if (performanceMonitor)
      performanceMonitor.startStep(`Data Mapping and Validation - Message ${messageIndex}`, {
        totalRecords: batch.length,
        mappingType: mappingConfig.mappingType
      });

    const validRecords = [];
    const mappingType = mappingConfig.mappingType;

    let recordIndex = 0;
    for (const record of batch) {
      recordIndex++;

      try {
        const mappedData = mapDataToModelFields(record, mappingConfig, mappingType, modelName);
        mappedData.updated_by = functionId;

        const validation = validateData(mappedData, mappingConfig);

        if (!validation.isValid) {
          results.failed++;
          const errorInfo = {
            record,
            errors: validation.errors,
            error: `Validation failed: ${validation.errors.join(', ')}`
          };
          results.errors.push(errorInfo);
          results.failedRecords.push(errorInfo);
          logger.error(`[TRACE ${traceId}] Record ${recordIndex} validation failed: ${validation.errors.join(', ')}`);
          continue;
        }

        // Store valid record with its mapped data
        validRecords.push({
          originalRecord: record,
          mappedData,
          recordIndex
        });

        // Log progress every 100 records
        if (performanceMonitor && recordIndex % 100 === 0) {
          performanceMonitor.logProgress(`Processed ${recordIndex}/${batch.length} records`, {
            validSoFar: validRecords.length,
            failedSoFar: results.failed,
            messageIndex: messageIndex
          });
        }

      } catch (error) {
        results.failed++;
        const errorInfo = {
          record,
          error: error.message
        };
        results.errors.push(errorInfo);
        results.failedRecords.push(errorInfo);
        logger.error(`[TRACE ${traceId}] Error mapping/validating record ${recordIndex}: ${error.message}`);
      }
    }
    if (performanceMonitor)
      performanceMonitor.endStep(`Data Mapping and Validation - Message ${messageIndex}`, {
        validRecords: validRecords.length,
        failedRecords: results.failed,
        validationErrors: results.errors.length
      });

    if (validRecords.length === 0) {
      logger.info(`[TRACE ${traceId}] No valid records to process`);
      if (performanceMonitor)
        performanceMonitor.complete({
          status: 'completed_no_valid_records',
          messageIndex,
          ...results
        });
      return results;
    }

    // Step 3: Database Query for Existing Records
    if (performanceMonitor)
      performanceMonitor.startStep(`Database Query - Existing Records - Message ${messageIndex}`, {
        validRecordsCount: validRecords.length,
        stagingKey
      });

    const stagingKeyValues = validRecords.map(r => r.mappedData[stagingKey]).filter(value => value); // Filter out null/undefined values
    logger.info(`[TRACE ${traceId}] Extracted ${stagingKeyValues.length} staging key values`);

    const existingRecords = await Model.findAll({
      where: {
        [stagingKey]: {
          [Op.in]: stagingKeyValues
        }
      }
    });

    logger.info(`[TRACE ${traceId}] Database query completed, found ${existingRecords.length} existing records`);

    const existingRecordsMap = new Map();
    existingRecords.forEach(record => {
      existingRecordsMap.set(record[stagingKey], record);
    });

    if (performanceMonitor)
      performanceMonitor.endStep(`Database Query - Existing Records - Message ${messageIndex}`, {
        existingRecordsFound: existingRecords.length,
        stagingKeyValuesQueried: stagingKeyValues.length
      });

    // Step 4: Record Classification (Create vs Update)
    if (performanceMonitor)
      performanceMonitor.startStep(`Record Classification - Message ${messageIndex}`, {
        totalValidRecords: validRecords.length
      });

    const recordsToCreate = [];
    const recordsToUpdate = [];

    for (const validRecord of validRecords) {
      const { originalRecord, mappedData } = validRecord;
      const existingRecord = existingRecordsMap.get(mappedData[stagingKey]);

      if (existingRecord) {
        recordsToUpdate.push({
          originalRecord,
          mappedData,
          existingRecord
        });
      } else {
        recordsToCreate.push({
          originalRecord,
          mappedData
        });
      }
    }
    if (performanceMonitor)
      performanceMonitor.endStep(`Record Classification - Message ${messageIndex}`, {
        recordsToCreate: recordsToCreate.length,
        recordsToUpdate: recordsToUpdate.length
      });

    // Step 5: Database Bulk Create Operation
    if (performanceMonitor && recordsToCreate.length > 0) {
      performanceMonitor.startStep(`Database Bulk Create - Message ${messageIndex}`, {
        recordsToCreate: recordsToCreate.length
      });

      try {
        const dataToCreate = recordsToCreate.map(r => r.mappedData);
        logger.info(`[TRACE ${traceId}] Prepared ${dataToCreate.length} records for bulk create`);

        await Model.bulkCreate(dataToCreate, { functionId, performanceMonitor });
        logger.info(`[TRACE ${traceId}] Bulk create operation completed`);

        recordsToCreate.forEach(r => {
          results.success++;
          results.successfulRecords.push(r.originalRecord);
        });

        logger.info(`[TRACE ${traceId}] Bulk created ${recordsToCreate.length} new ${modelName} records`);
        if (performanceMonitor)
          performanceMonitor.endStep(`Database Bulk Create - Message ${messageIndex}`, {
            recordsCreated: recordsToCreate.length,
            status: 'success'
          });

      } catch (error) {
        logger.error(`[TRACE ${traceId}] Error in bulk create: ${error.message}`);
        if (performanceMonitor)
          performanceMonitor.endStep(`Database Bulk Create - Message ${messageIndex}`, {
            recordsCreated: 0,
            status: 'failed',
            error: error.message
          });

        // Mark all create records as failed
        recordsToCreate.forEach(r => {
          results.failed++;
          const errorInfo = {
            record: r.originalRecord,
            error: `Bulk create failed: ${error.message}`
          };
          results.errors.push(errorInfo);
          results.failedRecords.push(errorInfo);
        });
      }
    }

    // Step 6: Database Bulk Update Operation
    if (performanceMonitor && recordsToUpdate.length > 0) {
      performanceMonitor.startStep(`Database Bulk Update - Message ${messageIndex}`, {
        recordsToUpdate: recordsToUpdate.length
      });

      try {
        const updatePromises = recordsToUpdate.map(async (r) => {
          return r.existingRecord.update(r.mappedData, { functionId, performanceMonitor });
        });

        logger.info(`[TRACE ${traceId}] Prepared ${updatePromises.length} update promises`);

        await Promise.all(updatePromises);
        logger.info(`[TRACE ${traceId}] Bulk update operation completed`);

        recordsToUpdate.forEach(r => {
          results.success++;
          results.successfulRecords.push(r.originalRecord);
        });

        logger.info(`[TRACE ${traceId}] Bulk updated ${recordsToUpdate.length} existing ${modelName} records`);
        if (performanceMonitor)
          performanceMonitor.endStep(`Database Bulk Update - Message ${messageIndex}`, {
            recordsUpdated: recordsToUpdate.length,
            status: 'success'
          });

      } catch (error) {
        logger.error(`[TRACE ${traceId}] Error in bulk update: ${error.message}`);
        if (performanceMonitor)
          performanceMonitor.endStep(`Database Bulk Update - Message ${messageIndex}`, {
            recordsUpdated: 0,
            status: 'failed',
            error: error.message
          });

        // Mark all update records as failed
        recordsToUpdate.forEach(r => {
          results.failed++;
          const errorInfo = {
            record: r.originalRecord,
            error: `Bulk update failed: ${error.message}`
          };
          results.errors.push(errorInfo);
          results.failedRecords.push(errorInfo);
        });
      }
    }

    // Step 7: Staging Table Updates
    if (performanceMonitor)
      performanceMonitor.startStep(`Staging Table Updates - Message ${messageIndex}`, {
        successfulRecords: results.successfulRecords.length,
        failedRecords: results.failedRecords.length
      });

    try {
      if (results.successfulRecords.length > 0) {
        await markRecordsAsProcessed(results.successfulRecords, agent);
        logger.info(`[TRACE ${traceId}] Marked ${results.successfulRecords.length} records as processed in staging`);
      }

      if (results.failedRecords.length > 0) {
        await markRecordsAsFailed(results.failedRecords, agent);
        logger.info(`[TRACE ${traceId}] Marked ${results.failedRecords.length} records as failed in staging`);
      }
      if (performanceMonitor)
        performanceMonitor.endStep(`Staging Table Updates - Message ${messageIndex}`, {
          processedRecordsMarked: results.successfulRecords.length,
          failedRecordsMarked: results.failedRecords.length,
          status: 'success'
        });

    } catch (stagingError) {
      logger.error(`[TRACE ${traceId}] Error updating staging table: ${stagingError.message}`);
      if (performanceMonitor)
        performanceMonitor.endStep(`Staging Table Updates - Message ${messageIndex}`, {
          processedRecordsMarked: 0,
          failedRecordsMarked: 0,
          status: 'failed',
          error: stagingError.message
        });

      // Don't throw here as the main processing was successful
    }

    // Calculate final metrics
    const successRate = batch.length > 0 ? (results.success / batch.length) * 100 : 0;

    logger.info(`[TRACE ${traceId}] Data processing complete. Success: ${results.success}, Failed: ${results.failed}`);
    logger.info(`[TRACE ${traceId}] Success Rate: ${Math.round(successRate * 100) / 100}%`);
    logger.info(`[TRACE ${traceId}] Records Processed: ${batch.length}`);
    logger.info(`[TRACE ${traceId}] Model: ${modelName}`);
    logger.info(`[TRACE ${traceId}] Mapping: ${mappingName}`);

    // Add final metrics to performance monitor
    if (performanceMonitor)
      performanceMonitor.addMetric(`Success Rate - Message ${messageIndex}`, successRate, {
        successful: results.success,
        failed: results.failed,
        total: batch.length,
        messageIndex
      });

    // Complete performance monitoring (only if using individual monitor)
    if (performanceMonitor && !context.collectionPerformanceMonitor) {
      performanceMonitor.complete({
        status: 'completed',
        successRate: Math.round(successRate * 100) / 100,
        modelName,
        mappingName,
        messageIndex,
        ...results
      });
    }

    return results;
  } catch (error) {
    logger.error(`[TRACE ${traceId}] Error in parseData function: ${error.message}`);
    logger.error(`[TRACE ${traceId}] Error stack: ${error.stack}`);

    // Complete performance monitoring with error (only if using individual monitor)
    if (performanceMonitor && !context.collectionPerformanceMonitor) {
      performanceMonitor.complete({
        status: 'failed',
        error: error.message,
        errorType: error.constructor.name,
        messageIndex
      });
    }

    throw error;
  }
};

module.exports = parseData;
