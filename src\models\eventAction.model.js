module.exports = (sequelize, DataTypes) => {
  const EventAction = sequelize.define(
    "EventAction",
    {
      event_action_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      event_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      function_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      tableName: "event_action",
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: false,
    }
  );

  EventAction.associate = (models) => {
    EventAction.belongsTo(models.Event, {
      foreignKey: "event_id",
      as: "event",
    });

    EventAction.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "event_action_status_name",
      constraints: false,
      scope: {
        group: "event_action_status", // Ensure the `status` group is consistent with master data
      },
    });
  };

  return EventAction;
};
