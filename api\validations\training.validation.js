const Joi = require("joi");
const { existsMasterData } = require("./custom.validation");

module.exports = {
  createTraining: {
    body: Joi.object().keys({
      name: Joi.string().required(),
      course_number: Joi.string().optional().allow(""),
      category: Joi.string().optional().allow(""),
      course_type: Joi.number().integer().external(existsMasterData("course_type")).optional(),
      recurrence: Joi.number().integer().external(existsMasterData("recurrence")).optional(),
      due_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      date_completed: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      score: Joi.number().min(0).max(100).optional(),
      status: Joi.number().integer().external(existsMasterData("training_status")).optional(),
      identity_id: Joi.string().uuid().required(),
      created_by: Joi.string().uuid().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  updateTraining: {
    params: Joi.object().keys({
      training_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      name: Joi.string().optional(),
      course_number: Joi.string().optional().allow(""),
      category: Joi.string().optional().allow(""),
      course_type: Joi.number().integer().external(existsMasterData("course_type")).optional(),
      recurrence: Joi.number().integer().external(existsMasterData("recurrence")).optional(),
      due_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      date_completed: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      score: Joi.number().min(0).max(100).optional(),
      status: Joi.number().integer().external(existsMasterData("training_status")).optional(),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  getTrainingById: {
    params: Joi.object().keys({
      training_id: Joi.string().uuid().required(),
    }),
  },

  deleteTraining: {
    params: Joi.object().keys({
      training_id: Joi.string().uuid().required(),
    }),
  },

  getTrainings: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      search: Joi.string().allow("").optional(),
      identity_id: Joi.string().uuid().optional(),
      status: Joi.string().valid("Pass", "Fail").optional(),
      category: Joi.string().optional(),
    }),
  },

};
