const symlinkDir = require("symlink-dir");
const path = require("path");
const fs = require("fs");
const logger = require("./src/config/logger");

const rootDir = __dirname;
const srcDir = path.join(rootDir, "src");
const filesDir = path.join(rootDir, "files"); // make sure this folder exists

// Array of projects to be symlinked
const projectNames = ["api", "processor", "agent"];

// Get command-line flag
const unlinkMode = process.argv.includes("--unlink");

/**
 * Dynamically symlink all folders inside the src directory for each project.
 */
async function handleFolderSymlinks(unlink = false) {
  // Read all items in the src directory and filter directories only
  let folders;
  try {
    folders = fs.readdirSync(srcDir).filter((item) => {
      const itemPath = path.join(srcDir, item);
      return fs.lstatSync(itemPath).isDirectory();
    });
  } catch (err) {
    logger.error("❌ Error reading src directory:", err);
    return;
  }

  for (const projectName of projectNames) {
    const projectPath = path.join(rootDir, projectName);

    for (const folderName of folders) {
      const src = path.join(srcDir, folderName);
      const linkPath = path.join(projectPath, folderName);

      try {
        if (fs.existsSync(linkPath) && fs.lstatSync(linkPath).isSymbolicLink()) {
          if (unlink) {
            fs.unlinkSync(linkPath);
            logger.info(`🗑️ Removed symlink: ${linkPath}`);
            continue;
          } else {
            const realTarget = fs.readlinkSync(linkPath);
            if (realTarget === src) {
              logger.info(`✓ Symlink already exists: ${linkPath}`);
              continue;
            } else {
              fs.rmSync(linkPath, { recursive: true, force: true });
            }
          }
        }

        if (!unlink) {
          await symlinkDir(src, linkPath);
          logger.info(`🔗 Created symlink: ${linkPath} → ${src}`);
        }
      } catch (err) {
        logger.error(`❌ Error handling symlink for folder: ${linkPath}`, err);
      }
    }
  }
}

/**
 * Symlink all .env.* files from the root directory for each project.
 */
function handleEnvSymlinks(unlink = false) {
  const envFiles = fs.readdirSync(rootDir).filter((f) => f.startsWith(".env."));
  if (envFiles.length === 0) {
    logger.warn("⚠️  No .env.* files found in root directory.");
    return;
  }

  for (const projectName of projectNames) {
    const projectPath = path.join(rootDir, projectName);

    for (const file of envFiles) {
      const src = path.join(rootDir, file);
      const dest = path.join(projectPath, file);

      try {
        if (fs.existsSync(dest)) {
          const stats = fs.lstatSync(dest);

          if (stats.isSymbolicLink()) {
            if (unlink) {
              fs.unlinkSync(dest);
              logger.info(`🗑️ Removed .env symlink: ${dest}`);
              continue;
            } else {
              const existingTarget = fs.readlinkSync(dest);
              if (existingTarget === src) {
                logger.info(`✓ Symlink already exists: ${dest}`);
                continue;
              } else {
                fs.unlinkSync(dest);
              }
            }
          } else {
            fs.rmSync(dest, { recursive: true, force: true });
            logger.info(`⚠️ Deleted existing file: ${dest}`);
          }
        }

        if (!unlink) {
          fs.symlinkSync(src, dest, "file");
          logger.info(`📄 Created symlink: ${dest} → ${src}`);
        }
      } catch (err) {
        logger.error(`❌ Error handling .env symlink for file: ${file}`, err);
      }
    }
  }
}

/**
 * Dynamically symlink all files inside the files directory for each project.
 */
function handleFileCopies(unlink = false) {
  // Check if filesDir exists
  if (!fs.existsSync(filesDir)) {
    logger.warn("⚠️  Files directory does not exist, skipping file copies.");
    return;
  }

  let files;
  try {
    files = fs.readdirSync(filesDir).filter((item) => {
      const itemPath = path.join(filesDir, item);
      return fs.lstatSync(itemPath).isFile();
    });
  } catch (err) {
    logger.error("❌ Error reading files directory:", err);
    return;
  }

  for (const projectName of projectNames) {
    const projectPath = path.join(rootDir, projectName);

    for (const file of files) {
      const src = path.join(filesDir, file);
      const dest = path.join(projectPath, file);

      try {
        if (fs.existsSync(dest)) {
          if (unlink) {
            fs.unlinkSync(dest);
            logger.info(`🗑️ Removed file: ${dest}`);
            continue;
          } else {
            // Remove the existing file to ensure overwrite
            fs.rmSync(dest, { recursive: true, force: true });
          }
        }

        if (!unlink) {
          // Copy the file instead of creating a symlink
          fs.copyFileSync(src, dest);
          logger.info(`📄 Copied file: ${dest} ← ${src}`);
        }
      } catch (err) {
        logger.error(`❌ Error handling file copy for: ${file}`, err);
      }
    }
  }
}


async function main() {
  try {
    logger.info(
      unlinkMode ? "🧹 Unlinking all symlinks..." : "🔧 Creating symlinks..."
    );

    await handleFolderSymlinks(unlinkMode);
    handleEnvSymlinks(unlinkMode);
    handleFileCopies(unlinkMode);

    logger.info(
      unlinkMode ? "✅ Symlinks removed." : "✅ Symlink setup complete."
    );
    process.exit(0);
  } catch (err) {
    logger.error("❌ Unexpected error:", err);
    process.exit(1);
  }
}

main();