const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const Patient = sequelize.define(
    "Patient",
    {
      patient_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      function_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "function",
          key: "function_id",
        },
        onDelete: "CASCADE",
      },
      title: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      first_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      middle_name: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      last_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      suffix: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      preferred_name: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      birth_date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      death_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      gender: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      gender_identity: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      marital_status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      race: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      ethnicity: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      preferred_language: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      birth_place: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      religion: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      nationality: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      // Administrative fields
      vip_status: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      consent_to_contact: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      primary_care_provider_id: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      preferred_pharmacy_id: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      // Common HL7 specific fields
      confidentiality_code: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      multiple_birth_indicator: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      birth_order: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      image: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: false,
      },

      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },

    },

    {
      tableName: "patient",
      timestamps: true,
      underscored: true,
    }
  );

  Patient.associate = (models) => {

    Patient.belongsTo(models.Function, {
      foreignKey: "function_id",
      as: "function",
    });

    // ONE‑to‑MANY: a Patient "has" many Appointments
    Patient.hasMany(models.Appointment, {
      foreignKey: "patient_id",
      as: "appointments",
    });

    // ONE‑to‑ONE: a Patient “has” one PatientIdentifier
    Patient.hasOne(models.PatientIdentifier, {
      foreignKey: "patient_id",
      as: "patientIdentifier",
    });

    Patient.hasOne(models.PatientAddress, {
      foreignKey: "patient_id",
      onDelete: "CASCADE",
      as: "patient_address",
    });

    Patient.belongsTo(models.MasterData, {
      foreignKey: "gender",
      targetKey: "key",
      as: "patient_gender_name",
      constraints: false,
      scope: {
        group: "patient_gender",
      },
    });

    Patient.belongsTo(models.MasterData, {
      foreignKey: "marital_status",
      targetKey: "key",
      as: "patient_marital_status_name",
      constraints: false,
      scope: {
        group: "patient_marital_status",
      },
    });
    Patient.belongsTo(models.MasterData, {
      foreignKey: "preferred_language",
      targetKey: "key",
      as: "patient_preferred_language_name",
      constraints: false,
      scope: {
        group: "patient_preferred_language",
      },
    });
    Patient.belongsTo(models.MasterData, {
      foreignKey: "confidentiality_code",
      targetKey: "key",
      as: "patient_confidentiality_code_name",
      constraints: false,
      scope: {
        group: "patient_confidentiality_code",
      },
    });

};

  media(Patient, sequelize, DataTypes);
  history(Patient, sequelize, DataTypes);

  return Patient;
};
