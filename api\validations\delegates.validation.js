const Joi = require("joi");
const { existsMasterData } = require("./custom.validation");

module.exports = {
  createDelegate: {
    body: Joi.object().keys({
      name: Joi.string().required(),
      task_to_delegate: Joi.string().required(),
      start_date: Joi.date().required(),
      end_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      status: Joi.number().integer().external(existsMasterData("delegate_status")).optional(),
      identity_id: Joi.string().uuid().required(),
      created_by: Joi.string().uuid().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  updateDelegate: {
    params: Joi.object().keys({
      delegate_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      name: Joi.string().optional(),
      task_to_delegate: Joi.string().optional(),
      start_date: Joi.date().optional(),
      end_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
    status: Joi.number().integer().external(existsMasterData("delegate_status")).optional(),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  getDelegateById: {
    params: Joi.object().keys({
      delegate_id: Joi.string().uuid().required(),
    }),
  },

  deleteDelegate: {
    params: Joi.object().keys({
      delegate_id: Joi.string().uuid().required(),
    }),
  },

  getDelegates: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      search: Joi.string().allow("").optional(),
      identity_id: Joi.string().uuid().optional(),
      status: Joi.string().valid("Active", "Inactive").optional(),
    }),
  },

};
