const Joi = require("joi");
const { unique, exists, existsMasterData } = require("./custom.validation");

// Validate facility and building existence as strings.
const facilityId = Joi.string().required().external(exists("Facility", "facility_id"));
const buildingId = Joi.string().required().external(exists("Building", "building_id"));

const create = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    name: Joi.string().required(),
    address: Joi.string().optional(),
    year_constructed: Joi.number().integer().optional(),
    building_code: Joi.string().required().external(unique("Building", "building_code")),
    status: Joi.number().integer().external(existsMasterData("building_status")).required(),
    type: Joi.number().integer().external(existsMasterData("building_type")).required(),
    occupancy_type: Joi.number().integer().external(existsMasterData("building_occupancy_type")).required(),
    phone: Joi.string().required(),
    email: Joi.string().email().required(),
    geo_location_code: Joi.number().optional(),
    other_code: Joi.string().optional(),
    building_url: Joi.string().uri().optional(),
    connected_applications: Joi.string().optional(),
    notes: Joi.string().optional(),
  }),
};

const facility = {
  params: Joi.object().keys({
    facilityId,
  }),
};

const building = {
  params: Joi.object().keys({
    facilityId,
    buildingId,
  }),
};

const update = {
  params: Joi.object().keys({
    facilityId,
    buildingId,
  }),
  body: Joi.object().keys({
    name: Joi.string().optional(),
    address: Joi.string().optional(),
    year_constructed: Joi.number().integer().optional(),
    building_code: Joi.string().optional(),
    status: Joi.number().integer().external(existsMasterData("building_status")).required(),
    type: Joi.number().integer().external(existsMasterData("building_type")).required(),
    occupancy_type: Joi.number().integer().external(existsMasterData("building_occupancy_type")).required(),
    phone: Joi.string().optional(),
    email: Joi.string().email().optional(),
    geo_location_code: Joi.number().optional(),
    other_code: Joi.string().optional(),
    building_url: Joi.string().uri().optional(),
    connected_applications: Joi.string().optional(),
    notes: Joi.string().optional(),
  }).min(1),
};

const status = {
  params: Joi.object().keys({
    facilityId,
    buildingId,
  }),
  body: Joi.object().keys({
    status: Joi.number().integer().external(existsMasterData("building_status")).required(),
  }),
};

module.exports = {
  create,
  building,
  facility,
  update,
  status,
};
