# Use official Node.js 18 Alpine image as base
FROM node:18-alpine

# Set working directory
WORKDIR /app
USER root

RUN apk add --no-cache git openssh-client # Add openssh-client if using SSH keys later

# Copy package.json and package-lock.json first to leverage Docker cache
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the project files
COPY . .
# COPY .env.local .env.local

# Clone the API repository using the CI_JOB_TOKEN
# This requires the CI_JOB_TOKEN variable to be passed during the build
ARG CI_JOB_TOKEN
RUN rm -rf api
RUN mkdir api && \
    git clone "https://gitlab-ci-token:${CI_JOB_TOKEN}@git.onetalkhub.com/care/api.git" api && \
    cd api && \
    git checkout develop


# Set environment variable for env file
ENV ENV_FILE=.env.local

# Run setup script to create symlinks
RUN npm run setup

RUN cd api && \
    npm install

# Expose the port the app runs on
EXPOSE 3001

# Start the backend server
# CMD ["node", "src/index.js"]
CMD ["sh","-c", "cd api && npm run dev"]