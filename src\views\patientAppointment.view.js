module.exports = (sequelize, DataTypes) => {
  const PatientAppointmentView = sequelize.define(
    "PatientAppointmentView",
    {
      appointment_id: {
        type: DataTypes.UUID,
        primaryKey: true,
      },
      patient_id: {
        type: DataTypes.UUID,
      },
      appointment_date: {
        type: DataTypes.DATE,
      },
      department: {
        type: DataTypes.STRING,
      },
      provider_name: {
        type: DataTypes.STRING,
      },
      appointment_status: {
        type: DataTypes.INTEGER,
      },
      appointment_status_name: {
        type: DataTypes.STRING,
      },
      type: {
        type: DataTypes.INTEGER,
      },
      appointment_type_name: {
        type: DataTypes.STRING,
      },
      facility_id: {
        type: DataTypes.UUID,
      },
      beds: {
        type: DataTypes.STRING,
      },
      updated_by: {
        type: DataTypes.UUID,
      },
      patient_name: {
          type: DataTypes.STRING,
          allowNull: true,
        },
      first_name: {
        type: DataTypes.STRING,
      },
      last_name: {
        type: DataTypes.STRING,
      },
      confidentiality_code: {
        type: DataTypes.INTEGER,
      },
      birth_date: {
        type: DataTypes.DATE,
      },
      patient_gender: {
        type: DataTypes.INTEGER,
      },
      mrn: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      facility_name: {
        type: DataTypes.STRING, 
        allowNull: true,
      },
      screening:{
        type: DataTypes.BOOLEAN, 
        allowNull: true,
      },
      image: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      death_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      arrival_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      departure_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      floor_number: {
        type: DataTypes.INTEGER, 
        allowNull: true,
      },
      building_name: {
        type: DataTypes.STRING, 
        allowNull: true,
      },
      room_number: {
        type: DataTypes.STRING, 
        allowNull: true,
      },
      address_line_1: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      address_line_2: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      country: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      postal_code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      phone:{
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      tableName: "view_patient_appointment",
      timestamps: false,
      underscored: true,
    }
  );

  return PatientAppointmentView;
};
