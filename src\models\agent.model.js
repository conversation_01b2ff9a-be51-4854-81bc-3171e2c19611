module.exports = (sequelize, DataTypes) => {
  const Agent = sequelize.define(
    'Agent',
    {
      agent_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: 'Program-friendly agent name (snake_case, no spaces) - used for agent identification',
      },
      type: {
        type: DataTypes.ENUM('Inbound', 'Outbound'),
        allowNull: false,
      },
      display_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      source: {
        type: DataTypes.ENUM('FileTransfer', 'URL', 'AzureBlob', 'AWSS3', 'Local', 'API'),
        allowNull: false,
      },
      queue: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      cron: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      stagging_key: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      batch_size: {
        type: DataTypes.INTEGER,
        defaultValue: 10,
        allowNull: false,
      },
      status: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        allowNull: false,
      },
      handler: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      mapping: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Name of the mapping configuration file (e.g., "hrData")',
      },
      schema: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: 'agent',
      timestamps: true,
      underscored: true,
    }
  );

  Agent.associate = function (models) {
    Agent.hasMany(models.AgentSetting, {
      foreignKey: 'agent_id',
      as: 'settings',
      onDelete: 'CASCADE'
    });

    Agent.hasMany(models.StagingData, {
      foreignKey: 'agent_id',
      as: 'stagingData',
      onDelete: 'CASCADE'
    });
  };

  return Agent;
};
