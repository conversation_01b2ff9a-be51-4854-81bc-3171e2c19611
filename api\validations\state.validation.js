const Joi = require("joi");
const { exists } = require("./custom.validation");

const stateId = Joi.string().required().external(exists("State", "state_id"));
const countryId = Joi.string().required().external(exists("Country", "country_id"));

const StateValidation = {
  create: {
    body: Joi.object().keys({
      country_id: countryId,
      name: Joi.string().required(),
    }),
  },
  state: {
    params: Joi.object().keys({
      stateId,
    }),
  },
  update: {
    params: Joi.object().keys({
      stateId,
    }),
    body: Joi.object().keys({
      name: Joi.string().optional(),
    }),
  },
};

module.exports = StateValidation;