const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const GlobalConfiguration = sequelize.define(
    "GlobalConfiguration",
    {
      global_configuration_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
      },
      display_name: {
        type: DataTypes.STRING(150),
        allowNull: false,
      },
      value: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "global_configuration",
      timestamps: true,
      underscored: true,
    }
  );

  history(GlobalConfiguration, sequelize, DataTypes);

  return GlobalConfiguration;
};
