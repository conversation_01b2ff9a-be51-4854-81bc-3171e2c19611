const express = require("express");
const validate = require("../middlewares/validate");
const { PatientGuestValidation } = require("../validations");
const {PatientGuestController} = require("../controllers");
const auth = require("../middlewares/auth");
const hipaaLogger = require("../middlewares/hipaaLogger");
const uploadToBase64 = require("../middlewares/upload");

const router = express.Router();


/**
 * @swagger
 * /appointments/guests:
 *   post:
 *     summary: Create a new guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GuestCreate'
 *     responses:
 *       201:
 *         $ref: '#/components/responses/GuestSuccess'
 */
router.post(
  "/guests/",
  auth("create_guest"),
  validate(PatientGuestValidation.create),
  PatientGuestController.create
);

/**
 * @swagger
 * /appointments/guests/add:
 *   post:
 *     summary: Add a Denied Guest or Friend
 *     description: This endpoint adds a denied guest or friend to the system.
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               guest_type:
 *                 type: integer
 *               birth_date:
 *                 type: string
 *                 format: date
 *                 example: "1990-01-01"
 *               reason:
 *                 type: string
 *               denied_on:
 *                 type: string
 *                 format: date-time
 *               patient_id:
 *                 type: string
 *               relationship_type:
 *                 type: integer
 *              
 *     responses:
 *       201:
 *         description: Denied Guest added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 patientGuest:
 *                   type: object
 *       400:
 *         description: Reason is required when guest_type is 2
 *       404:
 *         description: Guest not found
 */
router.post('/guests/add',   auth("create_guest"),validate(PatientGuestValidation.denied),  PatientGuestController.addGuest);

/**
 * @swagger
 * /appointments/guests/{appointment_guest_id}/check:
 *   patch:
 *     summary: Check-in or check-out a guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: appointment_guest_id
 *         in: path
 *         required: true
 *         description: The ID of the appointment guest to check in or check out.
 *         schema:
 *           type: string
 *           format: uuid
 *       - name: action
 *         in: query
 *         required: true
 *         description: The action to perform ("checkIn" or "checkOut").
 *         schema:
 *           type: string
 *           enum: [checkIn, checkOut]
 *       - name: appointment_id
 *         in: query
 *         required: false
 *         description: The appointment ID to assign the guest to during check-in if needed.
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         $ref: '#/components/responses/GuestSuccess'
 *       400:
 *         description: Invalid action specified or bad input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         $ref: '#/components/responses/NotFound'
 */
router.patch(
  "/guests/:appointment_guest_id/check",
  auth("update_guest"),
  validate(PatientGuestValidation.checkGuest), // Add validation
  PatientGuestController.checkGuest
);


/**
 * @swagger
 * /appointments/guests/{appointment_guest_screening_id}/screen/override:
 *   patch:
 *     summary: Override screening of a guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: appointment_guest_screening_id
 *         in: path
 *         required: true
 *         description: The ID of the guest to override screening for.
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 maxLength: 1000
 *                 description: The reason for overriding the screening.
 *     responses:
 *       200:
 *         description: Screening override successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Screening override successful."
 *       400:
 *         description: Invalid input or bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Guest or screening record not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal Server Error
 */
router.patch(
  "/guests/:appointment_guest_screening_id/screen/override",
  auth("override_screening"), 
  validate(PatientGuestValidation.overrideScreening),
  PatientGuestController.overrideScreening
);

/**
 * @swagger
 * /appointments/guests/{appointment_guest_screening_id}/override/revoke:
 *   patch:
 *     summary: Revoke override screening for a guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: appointment_guest_screening_id
 *         in: path
 *         required: true
 *         description: The ID of the guest screening to revoke override for.
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Override revoked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Override revoked successfully
 *       400:
 *         description: Screening not overridden or not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Screening not overridden or not found
 *       404:
 *         description: Guest not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guest not found
 */
router.patch(
  "/guests/:appointment_guest_screening_id/override/revoke",
  auth("override_screening"),
  PatientGuestController.revokeOverrideByPatient
);

/**
 * @swagger
 * /appointments/guests/{appointment_guest_screening_id}/screening/matches:
 *   get:
 *     summary: Get all the screening matches for a guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: appointment_guest_screening_id
 *         in: path
 *         required: true
 *         description: The ID of the guest to get screening matches for.
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: List of all screening matches (POI and Denied Guests)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       appointment_guest_id:
 *                         type: string
 *                         format: uuid
 *                       appointment_guest_screening_id:
 *                         type: string
 *                         format: uuid
 *                       appointment_guest_screening_match_id:
 *                         type: string
 *                         format: uuid
 *                       first_name:
 *                         type: string
 *                       last_name:
 *                         type: string
 *                       dob:
 *                         type: string
 *                         format: date
 *                       gender:
 *                         type: string
 *                         nullable: true
 *                       hair_color:
 *                         type: string
 *                         nullable: true
 *                       added_by:
 *                         type: string
 *                         nullable: true
 *                       match_type:
 *                         type: string
 *                         enum: [w, d]
 *                         description: "w for POI, d for Denied Guest"
 *       404:
 *         description: Guest not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
router.get(
  "/guests/:appointment_guest_screening_id/screening/matches",
  auth("view_screening_matches"),
  validate(PatientGuestValidation.getScreeningMatches), 
  PatientGuestController.getScreeningMatches
);


/**
 * @swagger
 * /appointments/guests/{patient_guest_id}/image:
 *   patch:
 *     summary: Update guest image
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: patient_guest_id
 *         in: path
 *         required: true
 *         description: The ID of the patient guest
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/GuestIdPath'  # If you have a reference for GuestIdPath, keep it; otherwise, you can remove this line.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Guest image updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Guest image updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/PatientGuest'  # Assuming PatientGuest schema is defined in components
 *       400:
 *         description: Bad Request, invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invalid input data"
 *       404:
 *         description: Guest not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Guest not found"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "An error occurred while updating the image"
 */
router.patch(
  "/guests/:patient_guest_id/image",
  auth("update_guest"),
  uploadToBase64("image"),
  validate(PatientGuestValidation.image),
  PatientGuestController.image
);

/**
 * @swagger
 * /appointments/guests/view:
 *   get:
 *     summary: Fetch appointment guests for a specific appointment and/or patient
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: guest_name
 *         schema:
 *           type: string
 *         description: Search guests by name
 *       - in: query
 *         name: guest_type
 *         schema:
 *           type: string
 *         description: Filter guests by type
 *       - in: query
 *         name: appointment_guest_status
 *         schema:
 *           type: string
 *         description: Filter guests by status
 *       - in: query
 *         name: appointment_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter guests by appointment ID
 *       - in: query
 *         name: patient_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Also include guests for this patient (in addition to appointment guests)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 100)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort guests by (e.g., "guest_arrival_time"). Default is "guest_arrival_time".
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Order of sorting for guests (ASC for ascending, DESC for descending). Default is "DESC".
 *     responses:
 *       200:
 *         description: Guest view data fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guest view data fetched successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           appointment_guest_id:
 *                             type: string
 *                             format: uuid
 *                           first_name:
 *                             type: string
 *                           last_name:
 *                             type: string
 *                           guest_type:
 *                             type: string
 *                           mrn:
 *                             type: string
 *                           guest_arrival_time:
 *                             type: string
 *                             format: date-time
 *                           guest_departure_time:
 *                             type: string
 *                             format: date-time
 *                           screening:
 *                             type: string
 *                           appointment_guest_status:
 *                             type: integer
 *                           appointment_guest_status_name:
 *                             type: string
 *                     meta:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 */
router.get(
  "/guests/view",
  auth("view_appointments"),
  validate(PatientGuestValidation.index), 
  hipaaLogger,
  PatientGuestController.index
);


/**
 * @swagger
 * /appointments/guests/search:
 *   get:
 *     summary: Search for guests
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         required: false
 *         schema:
 *           type: string
 *         description: Search guests by name, MRN, or other criteria
 *       - in: query
 *         name: facility_id
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the facility
 *     responses:
 *       200:
 *         description: Guests fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guests fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       first_name:
 *                         type: string
 *                       last_name:
 *                         type: string
 *                       guest_image:
 *                         type: string
 *                       mrn:
 *                         type: string
 *                       guest_arrival_time:
 *                         type: string
 *                         format: date-time
 *                       guest_departure_time:
 *                         type: string
 *                         format: date-time
 *                       screening:
 *                         type: string
 */
router.get(
  "/search",
  auth("view_appointments"),
  validate(PatientGuestValidation.search), 
  hipaaLogger,
  PatientGuestController.search
);

/**
 * @swagger
 * /appointments/guests/history:
 *   get:
 *     summary: Retrieve history for a specific patient guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient guest whose history is being retrieved.
 *     responses:
 *       200:
 *         description: Patient guest history retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient history retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     patient_guest_id:
 *                       type: string
 *                       format: uuid
 *                     formatted_identity:
 *                       type: string
 *                       example: "John Doe (EID123)"
 *       400:
 *         description: Bad request if patient_guest_id is missing or invalid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_guest_id is required
 *       404:
 *         description: No history found for the given patient guest.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No history found for the given patient guest
 */
router.get(
  "/guests/history",
  auth("view_guests"),
  validate(PatientGuestValidation.patientGuestHistory), 
  PatientGuestController.patientGuestHistory
);
/**
 * @swagger
 * /appointments/all_guests/history:
 *   get:
 *     summary: Retrieve history for a specific patient guest
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Patient guest history retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient history retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     patient_guest_id:
 *                       type: string
 *                       format: uuid
 *       400:
 *         description: Bad request if patient_guest_id is missing or invalid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_guest_id is required
 *       404:
 *         description: No history found for the given patient guest.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No history found for the given patient guest
 */
router.get(
  "/all_guests/history",
  auth("view_guests"),
  validate(PatientGuestValidation.patientAllGuestHistory),
  PatientGuestController.patientAllGuestHistory
);



module.exports = router;
