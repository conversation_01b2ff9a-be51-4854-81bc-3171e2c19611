module.exports = (sequelize, DataTypes) => {
  const Event = sequelize.define(
    "Event",
    {
      event_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      trace_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      parent_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      child_id: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      event_type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      params: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      order: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      queue: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "event",
      timestamps: true,
      createdAt: "created_at",
      updatedAt: false,
      underscored: true,
    }
  );

  return Event;
};
