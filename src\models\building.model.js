const {
  BuildingType,
  BuildingOccupancyType,
  Status,
} = require("../config/attributes");
const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const Building = sequelize.define(
    "Building",
    {
      building_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      address: {
        type: DataTypes.STRING,
      },
      year_constructed: {
        type: DataTypes.INTEGER,
      },
      building_code: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      type: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      occupancy_type: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      phone: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      geo_location_code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      other_code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      building_url: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      connected_applications: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "building",
      timestamps: true,
      underscored: true,
    }
  );

  Building.associate = (models) => {
    Building.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
      onDelete: "CASCADE",
    });
    Building.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "building_status_name",
      constraints: false,
      scope: { group: "building_status" },
    });

    Building.belongsTo(models.MasterData, {
      foreignKey: "type",
      targetKey: "key",
      as: "building_type_name",
      constraints: false,
      scope: { group: "building_type" },
    });

    Building.belongsTo(models.MasterData, {
      foreignKey: "occupancy_type",
      targetKey: "key",
      as: "building_occupancy_type_name",
      constraints: false,
      scope: { group: "building_occupancy_type" },
    });
  };

  history(Building, sequelize, DataTypes);

  return Building;
};
