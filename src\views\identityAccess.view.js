// views/identityAccessView.view.js
module.exports = (sequelize, DataTypes) => {
  const IdentityAccessView = sequelize.define(
    "IdentityAccessView",
    {
      identity_access_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      pacs_area_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      online: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      requestable_self_service: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      access_level_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      facility_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      system_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      card_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      tableName: "view_identity_access",
      timestamps: false,
      underscored: true,
      freezeTableName: true,
      createdAt: false,
      updatedAt: false,
      // Tells Sequelize not to expect a primary key
      primaryKey: false,
    }
  );

  // Explicitly remove default `id` requirement
  IdentityAccessView.removeAttribute("id");

  return IdentityAccessView;
};
