const logger = require("../../config/logger");

module.exports = (model, sequelize, DataTypes, excludedOptons=[]) => {
  // Helper function to convert values into strings for storage.
  const stringifyValue = (val) => {
    if (val === null || val === undefined) return null;
    return typeof val === "object" ? JSON.stringify(val) : String(val);
  };

  // Determine the name for the original key column.
  // If the model's primary key is "id", use "id" to avoid conflict.
  const originalKeyColumn = model.primaryKeyAttribute === "id" ? "id" : model.primaryKeyAttribute;

  // Utility to extract the identity id from options or instance.
  const getCreatedBy = (instance, options) =>
    options.updated_by ||
    instance.get("updated_by") ||
    null;

  model.rawAttributes.created_by = { type: DataTypes.UUID, allowNull: true };
  model.refreshAttributes();

  // Exclude fields from per-field logging.
  const excludeFields = [model.primaryKeyAttribute, "updatedAt", "createdAt", 'created_by', 'updated_by', ...excludedOptons];

  // Define the transaction table.
  const transaction_id = model.getTableName() + "_transaction_id";
  const Transaction = sequelize.define(
    model.getTableName() + "_transaction",
    {
      [transaction_id]: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
      },
      [originalKeyColumn]: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      operation: {
        type: DataTypes.ENUM('0', '1', '2'),
        allowNull: false,
        comment: "0: Create, 1: Update, 2: Delete",
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      freezeTableName: true,
      timestamps: true,
      underscored: true,
    }
  );

  // Define the history table.
  const history_id = model.getTableName() + "_history_id";
  const History = sequelize.define(
    model.getTableName() + "_history",
    {
      [history_id]: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
      },
      [transaction_id]: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      column_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      new_value: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      old_value: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      freezeTableName: true,
      timestamps: true,
      underscored: true,
    }
  );

  // Hook: After creation, record a transaction then for each field log its new value.
  model.addHook("afterCreate", async (instance, options) => {
    try {
      const data = instance.get();
      const createdBy = getCreatedBy(instance, options);
      const primaryKeyValue = instance.get(model.primaryKeyAttribute);

      // Create a transaction record for the 0 operation.
      const transactionRecord = await Transaction.create(
        {
          [originalKeyColumn]: primaryKeyValue,
          operation: '0',
          updated_by: createdBy,
        },
        { transaction: options.transaction }
      );
      
      const historyRecords = [];
      Object.keys(data).forEach((key) => {
        if (excludeFields.includes(key)) return;
        historyRecords.push({
          [transaction_id]: transactionRecord[transaction_id],
          column_name: key,
          new_value: stringifyValue(data[key]),
          old_value: null,
        });
      });
      if (historyRecords.length > 0) {
        await History.bulkCreate(historyRecords, { transaction: options.transaction });
      }
    } catch (error) {
      logger.error("Error recording create history:", error);
    }
  });

  // Hook: After update, record a transaction and then log only the changed fields with new and old values.
  model.addHook("afterUpdate", async (instance, options) => {
    try {
      const changedFields = instance.changed();
      if (!changedFields) return; // No changes to record.
      const createdBy = getCreatedBy(instance, options);
      const primaryKeyValue = instance.get(model.primaryKeyAttribute);

      // Create a transaction record for the 1 operation.
      const transactionRecord = await Transaction.create(
        {
          [originalKeyColumn]: primaryKeyValue,
          operation: '1',
          updated_by: createdBy,
        },
        { transaction: options.transaction }
      );

      const historyRecords = [];
      changedFields.forEach((field) => {
        if (excludeFields.includes(field)) return;
        const newVal = instance.getDataValue(field);
        const oldVal = instance._previousDataValues[field];
        historyRecords.push({
          [transaction_id]: transactionRecord[transaction_id],
          column_name: field,
          new_value: stringifyValue(newVal),
          old_value: stringifyValue(oldVal),
        });
      });
      if (historyRecords.length > 0) {
        await History.bulkCreate(historyRecords, { transaction: options.transaction });
      }
    } catch (error) {
      logger.error("Error recording update history:", error);
    }
  });

  // Hook: Before deletion, record a transaction then log each field's old value.
  model.addHook("beforeDestroy", async (instance, options) => {
    try {
      const data = instance.get();
      const createdBy = getCreatedBy(instance, options);
      const primaryKeyValue = instance.get(model.primaryKeyAttribute);

      // Create a transaction record for the 2 operation.
      const transactionRecord = await Transaction.create(
        {
          [originalKeyColumn]: primaryKeyValue,
          operation: '2',
          updated_by: createdBy,
        },
        { transaction: options.transaction }
      );

      const historyRecords = [];
      Object.keys(data).forEach((key) => {
        if (excludeFields.includes(key)) return;
        historyRecords.push({
          [transaction_id]: transactionRecord[transaction_id],
          column_name: key,
          new_value: null,
          old_value: stringifyValue(data[key]),
        });
      });
      if (historyRecords.length > 0) {
        await History.bulkCreate(historyRecords, { transaction: options.transaction });
      }
    } catch (error) {
      logger.error("Error recording delete history:", error);
    }
  });

  model.addHook("beforeCreate", (instance, options) => {
    const user = getCreatedBy(instance, options);
    if (user) {
      instance.set("created_by", user);
    }
  });
 
  // Return both models so they can be used elsewhere if needed.
  return { Transaction, History };
};
