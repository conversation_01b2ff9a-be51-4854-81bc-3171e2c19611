# CareMate API

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Express.js](https://img.shields.io/badge/Express.js-4.21+-blue.svg)](https://expressjs.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Database-blue.svg)](https://www.postgresql.org/)
[![Sequelize](https://img.shields.io/badge/Sequelize-6.37+-orange.svg)](https://sequelize.org/)
[![JWT](https://img.shields.io/badge/JWT-Authentication-red.svg)](https://jwt.io/)
[![RabbitMQ](https://img.shields.io/badge/RabbitMQ-Message%20Queue-orange.svg)](https://www.rabbitmq.com/)
[![Tyk](https://img.shields.io/badge/Tyk-API%20Gateway-blue.svg)](https://tyk.io/)

CareMate API is a comprehensive healthcare management system backend that provides robust authentication, patient management, appointment scheduling, visitor management, and facility administration capabilities. The system features a sophisticated rule engine, dual authentication modes, and enterprise-grade scalability with message queuing and API gateway integration.

## 🏗️ Architecture Overview

The CareMate API follows a sophisticated layered architecture with enterprise-grade features including dual authentication, rule engine, and message queuing.

### 🔄 Request Flow Architecture

The system processes requests through a well-defined pipeline ensuring security, validation, and business logic execution:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Tyk Gateway   │───▶│  Express Routes  │───▶│ Auth Middleware│
│   (Optional)    │    │                  │    │ (Auth + Authz)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         |
                                                         |
                     ┌────────────────────────┐          |
                     |       catchAsync       |          ▼
                     │  ┌──────────────────┐  | ┌─────────────────┐
                     │  │    Controllers   │◀───│  Validation     │
                     │  │                  │  | │   Middleware    │
                     │  └──────────────────┘  | └─────────────────┘
                     │                        |
                     └────────────────────────┘
                                │                        
                                ▼                        
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│    Services     │◀───│   Data Models    │───▶│   Rule Engine  │
│                 │    │   + Plugins      │    │   Triggers      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   PostgreSQL     │    │   Event Queue   │
                       │   Database       │    │   (RabbitMQ)    │
                       └──────────────────┘    └─────────────────┘
```

**Flow Explanation:**
1. **Tyk Gateway** (Optional) - Rate limiting, analytics, and SSO proxy
2. **Express Routes** - RESTful API endpoint routing
3. **Auth Middleware** - Combined authentication and authorization
4. **Validation Middleware** - Request input validation using Joi schemas
5. **Controllers** - Request handling and response formatting
6. **catchAsync** - Transaction management and exception handling
7. **Services** - Business logic and external integrations
8. **Data Models + Plugins** - Database operations with history, media, pagination, and triggers
9. **Rule Engine** - Business rules evaluation and event generation
10. **PostgreSQL** - Primary data storage
11. **RabbitMQ** - Asynchronous event processing

### 🏛️ Architectural Layers

#### 1. **API Gateway Layer** (Optional)
- **Tyk Gateway**: Enterprise API management with rate limiting and analytics
- **Health Checks**: Connection monitoring on application initialization
- **SSO Proxy**: Handles SSO authentication when Tyk mode is enabled

#### 2. **Presentation Layer**
- **Express.js Routes**: RESTful API endpoints with comprehensive Swagger documentation
- **Middleware Stack**: Authentication, authorization, validation, rate limiting, CORS
- **Dual Rate Limiting**: Adaptive rate limiting based on authentication mode

#### 3. **Security Layer**
- **Dual Authentication**: Configurable switching between custom JWT and Tyk gateway
- **Multiple SSO Providers**: SAML, OpenID Connect, Azure AD with single-line integration
- **Role-Based Access Control**: Dynamic permissions embedded in JWT tokens
- **HIPAA Compliance**: Comprehensive audit logging and data protection

#### 4. **Validation Layer**
- **Joi Schema Validation**: Comprehensive input validation for all endpoints
- **Custom Validators**: Master data validation, UUID validation, business rule validation
- **Validation-First Architecture**: All requests validated before processing

#### 5. **Business Logic Layer**
- **Service Classes**: Encapsulated business logic and external integrations
- **catchAsync**: Automatic transaction management and exception handling
- **Master Data Management**: Centralized reference data with caching
- **Performance Monitoring**: Built-in performance tracking and optimization

#### 6. **Data Access Layer**
- **Sequelize ORM**: Advanced PostgreSQL integration with read/write splitting
- **Model Plugins**: History tracking, pagination, media handling, rule triggers
- **Database Views**: Complex queries optimized through PostgreSQL views
- **Migration System**: Version-controlled database schema management

#### 7. **Rule Engine & Event Layer**
- **JSON Rules Engine**: Dynamic business rules with fact validation
- **Event Generation**: Automatic event creation based on model operations
- **Message Queuing**: RabbitMQ for asynchronous event processing
- **Bulk Processing**: Optimized bulk operations with dependency analysis

## 📁 Project Structure

```
caremate-api/
├── 📁 config/                    # Configuration files
│   ├── attributes.js             # Database attribute configurations
│   ├── caching.js               # Cache configuration
│   ├── config.js                # Main application configuration
│   ├── database.js              # Database connection setup
│   ├── logger.js                # Winston logger configuration
│   ├── morgan.js                # HTTP request logger
│   ├── passport.js              # Authentication strategies
│   ├── permissions.js           # Permission definitions
│   ├── rabbitmq.js             # Message queue configuration
│   └── sequelize.js             # Sequelize ORM setup
├── 📁 controllers/               # Request handlers
│   ├── accessLevel.controller.js
│   ├── appointment.controller.js
│   ├── auth.controller.js
│   ├── building.controller.js
│   ├── facility.controller.js
│   ├── guest.controller.js
│   ├── identity.controller.js
│   ├── patient.controller.js
│   ├── visit.controller.js
│   └── ... (20+ controllers)
├── 📁 docs/                     # API documentation
│   ├── components.yml           # Swagger components
│   └── swaggerDef.js           # Swagger configuration
├── 📁 helpers/                  # Utility functions
│   ├── agent.helper.js
│   ├── api.helper.js
│   ├── caching.helper.js
│   ├── global.helper.js
│   └── hl7.helper.js
├── 📁 middlewares/              # Express middleware
│   ├── auth.js                  # Authentication middleware
│   ├── error.js                 # Error handling
│   ├── hipaaLogger.js          # HIPAA compliance logging
│   ├── rateLimiter.js          # Rate limiting
│   ├── upload.js               # File upload handling
│   └── validate.js             # Request validation
├── 📁 migrations/        # Database migrations for creating views
├── 📁 models/                   # Sequelize models
│   ├── identity.model.js
│   ├── patient.model.js
│   ├── appointment.model.js
│   ├── facility.model.js
│   └── ... (50+ models)
├── 📁 routes/                   # API routes
│   ├── auth.route.js
│   ├── patient.route.js
│   ├── appointment.route.js
│   └── ... (25+ route files)
├── 📁 scripts/                  # Utility scripts
│   └── dbSync.js               # Database synchronization
├── 📁 seeders/                  # Database seeders
├── 📁 services/                 # Business logic
│   ├── auth.service.js
│   ├── csv.service.js             #related to seprate project (agent)
│   ├── encryption.service.js      #related to seprate project (agent)
│   ├── event.service.js
│   ├── performance.service.js     #related to seprate project (agent, processor)
│   └── staging.service.js         #related to seprate project (agent, processor)
├── 📁 validations/              # Request validation schemas
├── 📁 views/                    # Database views
├── 📄 app.js                    # Express application setup
├── 📄 server.js                 # Server entry point
├── 📄 package.json              # Dependencies and scripts
├── 📄 Dockerfile               # Docker configuration
└── 📄 README.md                # This file
```

## 🔧 Core Features

### 🔐 Authentication & Authorization
- **Dual Authentication System**: Custom JWT + Tyk API Gateway
- **Multi-Provider SSO**: SAML, OpenID Connect, Azure AD
- **Role-Based Access Control (RBAC)**: Dynamic permissions system
- **JWT Token Management**: Access and refresh token handling
- **Session Management**: Secure session handling with activity logging

### 🏥 Healthcare Management
- **Patient Management**: Comprehensive patient records and history
- **Appointment Scheduling**: Advanced appointment booking system
- **Visitor Management**: Guest registration and tracking
- **Facility Management**: Multi-facility support with access levels
- **Document Management**: Secure document storage and retrieval

### 🏢 Enterprise Features
- **Multi-Tenancy**: Facility-based data isolation
- **Audit Logging**: HIPAA-compliant activity tracking
- **Caching System**: Redis/Memcached for performance
- **Message Queuing**: RabbitMQ for asynchronous processing
- **Rate Limiting**: API protection and throttling

## 🛠️ Technology Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Runtime** | Node.js | 18+ | JavaScript runtime |
| **Framework** | Express.js | 4.21+ | Web application framework |
| **Database** | PostgreSQL | Latest | Primary database |
| **ORM** | Sequelize | 6.37+ | Database abstraction |
| **Authentication** | JWT | 9.0+ | Token-based auth |
| **Caching** | Redis/Memcached | Latest | Performance optimization |
| **Message Queue** | RabbitMQ | Latest | Asynchronous processing |
| **Documentation** | Swagger | Latest | API documentation |
| **Security** | Helmet | 8.0+ | Security headers |
| **Validation** | Joi | 17.13+ | Request validation |
| **Logging** | Winston | 3.17+ | Application logging |
| **Testing** | Jest/Mocha | Latest | Unit and integration testing |

## 📋 Prerequisites

Before running the CareMate API, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **npm** (v8 or higher)
- **PostgreSQL** (v12 or higher)
- **Redis** (optional, for caching)
- **RabbitMQ** (optional, for message queuing)

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone https://git.onetalkhub.com/care/api.git
cd caremate-api
npm install
```

### 2. Environment Configuration
Create your environment file:
```bash
cp .env.local .env
```

### 3. Database Setup
```bash
# Sync database schema
npm run db

# Run migrations (if any)
npx sequelize-cli db:migrate

# Seed initial data
npx sequelize-cli db:seed:all
```

### 4. Start Development Server
```bash
npm run dev
```

The API will be available at `http://localhost:3001`

## 🔧 Configuration

### Environment Configuration

The CareMate API uses a comprehensive environment-based configuration system supporting multiple deployment scenarios.

#### Core Application Settings
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Application environment (local/development/production) | `local` | ✅ |
| `PORT` | Server port | `3001` | ✅ |
| `DEFAULT_SERVER_URL` | Default API base URL | `http://localhost:3001/api` | ✅ |

#### Authentication Configuration
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `AUTH_MODE` | Authentication mode (`custom` or `tyk`) | `custom` | ✅ |
| `TYK_URL` | Tyk Gateway URL | `http://localhost:8181` | ⚠️ |
| `PROTECTED_SERVER_URL` | Tyk protected endpoint URL | - | ⚠️ |
| `PUBLIC_SERVER_URL` | Tyk public endpoint URL | - | ⚠️ |

#### Database Configuration (Read/Write Split)
| Variable | Description | Required |
|----------|-------------|----------|
| `DB_WRITE_HOST` | Write database host | ✅ |
| `DB_WRITE_USERNAME` | Write database username | ✅ |
| `DB_WRITE_PASSWORD` | Write database password | ✅ |
| `DB_WRITE_DATABASE` | Write database name | ✅ |
| `DB_READ_HOST` | Read database host | ✅ |
| `DB_READ_USERNAME` | Read database username | ✅ |
| `DB_READ_PASSWORD` | Read database password | ✅ |
| `DB_READ_DATABASE` | Read database name | ✅ |

#### JWT Configuration
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `JWT_SECRET` | JWT signing secret | - | ✅ |
| `JWT_ACCESS_EXPIRATION_MINUTES` | Access token expiry | `300000000` | ✅ |
| `JWT_REFRESH_EXPIRATION_DAYS` | Refresh token expiry | `30` | ✅ |

#### SSO Provider Configuration
| Variable | Description | Required |
|----------|-------------|----------|
| `SAML_ENTRY_POINT` | SAML identity provider entry point | ⚠️ |
| `SAML_ISSUER` | SAML application issuer | ⚠️ |
| `SAML_CERT` | SAML certificate | ⚠️ |
| `SAML_CALLBACK_URL` | SAML callback URL | ⚠️ |
| `OIDC_ISSUER` | OpenID Connect issuer | ⚠️ |
| `OIDC_CLIENT_ID` | OpenID Connect client ID | ⚠️ |
| `OIDC_CLIENT_SECRET` | OpenID Connect client secret | ⚠️ |
| `AZURE_TENANT_ID` | Azure AD tenant ID | ⚠️ |
| `AZURE_CLIENT_ID` | Azure AD client ID | ⚠️ |
| `AZURE_CLIENT_SECRET` | Azure AD client secret | ⚠️ |

#### Message Queue Configuration
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MESSAGE_QUEUING` | Enable message queuing | `true` | ⚠️ |
| `RABBITMQ_URL` | RabbitMQ connection URL | - | ⚠️ |
| `CONCURRENCY_LIMIT` | Message processing concurrency | `15` | ⚠️ |

#### Caching Configuration
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `CACHE_DRIVER` | Cache driver (`redis`, `memcached`, `memory`) | `memory` | ❌ |
| `CACHE_TTL` | Cache time-to-live (seconds) | `600` | ❌ |
| `REDIS_HOST` | Redis host | `localhost` | ⚠️ |
| `REDIS_PORT` | Redis port | `6379` | ⚠️ |

**Legend**: ✅ Required | ⚠️ Optional (feature-dependent) | ❌ Optional

## 🔐 Authentication Architecture

### Passport.js Authentication Strategies

The CareMate API implements a comprehensive authentication system using Passport.js with multiple strategies:

#### 1. **JWT Strategy (Custom Mode)**
- Bearer token authentication with embedded permissions in JWT payload
- Automatic token validation and user context injection
- Refresh token mechanism for extended sessions

#### 2. **SAML Strategy**
- Enterprise SAML 2.0 SSO integration with identity provider federation
- Automatic user provisioning and attribute mapping from SAML assertions

#### 3. **OpenID Connect Strategy**
- Modern OAuth2/OpenID Connect flow with multiple identity provider support
- Automatic token refresh and user profile synchronization

#### 4. **Azure AD Strategy**
- Native Azure AD integration with multi-tenant support
- Conditional access policy support and Microsoft Graph API integration

### Dual Authentication System

The CareMate API implements a sophisticated dual authentication system that can seamlessly switch between custom JWT authentication and Tyk API Gateway authentication based on configuration.

#### Authentication Modes

**Custom Authentication Mode (`AUTH_MODE=custom`)**:
- Direct JWT-based authentication with full control over the authentication flow
- Internal user management with custom login/logout endpoints
- Suitable for standalone deployments and development environments
- Complete control over token generation, validation, and refresh mechanisms
- Integrated with Passport.js strategies for SSO providers

**Tyk Gateway Mode (`AUTH_MODE=tyk`)**:
- API Gateway handles authentication with header-based identity validation
- Enterprise-grade rate limiting, analytics, and monitoring
- Centralized authentication across microservices architecture
- Suitable for production enterprise deployments
- SSO endpoints proxied through Tyk while maintaining custom auth for other endpoints

#### Intelligent Authentication Switching

The authentication middleware automatically detects the configured mode and applies the appropriate authentication strategy:

**Custom Mode Flow**:
1. Extract JWT token from Authorization header
2. Validate token using Passport JWT strategy
3. Verify user permissions against required rights
4. Inject user identity into request context

**Tyk Mode Flow**:
1. Validate Tyk-specific headers (x-caremate-identity-id, x-caremate-permissions)
2. Extract user identity and permissions from headers
3. Verify permissions against required rights
4. Inject user identity into request context

#### Benefits of Dual Authentication

- **Deployment Flexibility**: Single codebase supports multiple deployment scenarios
- **Zero Code Changes**: Switch authentication modes via environment configuration
- **Enterprise Ready**: Seamless integration with enterprise API gateways
- **Development Friendly**: Use custom auth for development, Tyk for production
- **Gradual Migration**: Migrate from custom to gateway authentication incrementally

### Permission System

**JWT Embedded Permissions:**
```json
{
  "sub": "user-uuid",
  "permissions": ["patient:read", "appointment:write", "facility:admin"],
  "type": "access",
  "iat": **********,
  "exp": **********
}
```

**Tyk Header Permissions:**
```http
x-caremate-identity-id: user-uuid
x-caremate-permissions: patient:read,appointment:write,facility:admin
x-caremate-authorized: true
```

### Database Configuration

The system supports read/write database splitting for performance:

```env
# Write Database (Master)
DB_WRITE_HOST=your-write-db-host
DB_WRITE_USERNAME=your-username
DB_WRITE_PASSWORD=your-password
DB_WRITE_DATABASE=your-database

# Read Database (Replica)
DB_READ_HOST=your-read-db-host
DB_READ_USERNAME=your-username
DB_READ_PASSWORD=your-password
DB_READ_DATABASE=your-database
```

## 📚 API Documentation

### Swagger Documentation
Access the interactive API documentation at:
- **Local**: [http://localhost:3001/docs](http://localhost:3001/docs)
- **Development**: [https://api.onetalkhub.com/docs](https://api.onetalkhub.com/docs)

### Authentication Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/auth/login` | User login with email/password |
| `POST` | `/api/auth/register` | User registration |
| `POST` | `/api/auth/logout` | User logout |
| `POST` | `/api/auth/refresh-tokens` | Refresh access tokens |
| `GET` | `/api/auth/saml` | SAML SSO login |
| `GET` | `/api/auth/oidc` | OpenID Connect login |
| `GET` | `/api/auth/azure` | Azure AD login |

### Core API Endpoints
| Resource | Base Path | Description |
|----------|-----------|-------------|
| **Patients** | `/api/patients` | Patient management |
| **Appointments** | `/api/appointments` | Appointment scheduling |
| **Facilities** | `/api/facilities` | Facility management |
| **Guests** | `/api/guests` | Visitor management |
| **Identity** | `/api/identity` | User identity management |
| **Access Levels** | `/api/access-levels` | Permission management |
| **Buildings** | `/api/buildings` | Building management |
| **Rooms** | `/api/rooms` | Room management |
| **Vehicles** | `/api/vehicles` | Vehicle tracking |
| **Documents** | `/api/documents` | Document management |

## 🗄️ Database Management

### Database Conventions & Standards

The CareMate API follows strict database naming conventions to ensure consistency and maintainability across the entire system.

#### Table Naming Conventions

**Rule: All table names are singular**
```sql
-- ✅ Correct
patient
appointment
facility
guest_visit
appointment_guest

-- ❌ Incorrect
patients
appointments
facilities
guest_visits
appointment_guests
```

#### Column Naming Conventions

**Rule: All column names use snake_case**
```sql
-- ✅ Correct
first_name
last_name
created_at
updated_at
facility_id
appointment_date

-- ❌ Incorrect
firstName
lastName
createdAt
updatedAt
facilityId
appointmentDate
```

#### Primary Key Naming Convention

**Rule: Primary keys follow the pattern `{table_name}_id`**
```sql
-- ✅ Correct
patient_id          -- for patient table
appointment_id      -- for appointment table
facility_id         -- for facility table
guest_visit_id      -- for guest_visit table
appointment_guest_id -- for appointment_guest table

-- ❌ Incorrect
id
patient_pk
appointmentId
facility_uuid
```

#### Foreign Key Naming Convention

**Rule: Foreign keys reference the primary key name of the target table**
```sql
-- ✅ Correct
patient_id          -- references patient.patient_id
facility_id         -- references facility.facility_id
appointment_id      -- references appointment.appointment_id

-- ❌ Incorrect
patient
facility
appointment_ref
```

#### Junction Table Naming Convention

**Rule: Junction tables combine both table names in alphabetical order**
```sql
-- ✅ Correct
appointment_guest   -- links appointment and guest
guest_visit        -- links guest and visit
patient_identifier -- links patient and identifier

-- ❌ Incorrect
guest_appointment
visit_guest
identifier_patient
```

### Database Synchronization
```bash
# Sync database schema (safe)
npm run db

# Sync with specific environment
npm run db -- --env-file .env.dev

# Refresh database (drops and recreates tables)
npm run db:refresh

# Refresh specific models
npm run db:refresh -- --model Facility Floor Room
```

### Database Reset Operations
```bash
# Reset default database
npm run db:reset

# Reset all databases
npm run db:reset:all

# Reset databases in parallel (fastest)
npm run db:reset:parallel

# Reset specific database
npm run db:reset:local
npm run db:reset:core
```

### Migrations and Seeders

#### Migration Commands
```bash
# Run migrations
npx sequelize-cli db:migrate

# Undo last migration
npx sequelize-cli db:migrate:undo

# Create new migration
npx sequelize-cli migration:generate --name create-patient-table
```


### Seeder Naming Conventions & Types

The CareMate API uses a structured seeder naming convention to distinguish between different types of data seeding:

#### Seeder Naming Rules

**Essential/Mandatory Seeders (No "sample" in name)**
```
**************-seed-master-data.js
**************-seed-functions.js
**************-seed-permissions.js
20230409123459-seed-event-config.js
20230409123460-seed-application-config.js
```

**Sample Data Seeders (Contains "sample" in name)**
```
20230409123461-seed-sample-patients.js
20230409123462-seed-sample-appointments.js
20230409123463-seed-sample-facilities.js
20230409123464-seed-sample-users.js
```

#### Seeder Categories

**1. Essential Configuration Seeders**
- **Master Data Seeder**: Seeds reference data for dropdowns, status values, and lookup tables
- **Function Seeder**: Seeds application functions and endpoints for permission mapping
- **Permission Seeder**: Seeds role-based permissions and access control data
- **Event Configuration Seeder**: Seeds event types and queue configurations for rule engine
- **Application Configuration Seeder**: Seeds global application settings and configurations

**2. Sample Data Seeders**
- **Sample Patients**: Demo patient records for testing and development
- **Sample Appointments**: Demo appointment data with various statuses
- **Sample Facilities**: Demo healthcare facilities and locations
- **Sample Users**: Demo user accounts with different roles and permissions

#### Seeder Execution Strategy

**Production Environment**:
```bash
# Run only essential seeders (exclude sample data)
npx sequelize-cli db:seed --seed **************-seed-master-data.js
npx sequelize-cli db:seed --seed **************-seed-functions.js
npx sequelize-cli db:seed --seed **************-seed-permissions.js
```

**Development Environment**:
```bash
# Run all seeders including sample data
npx sequelize-cli db:seed:all
```

#### Benefits of Seeder Convention

- **Environment Safety**: Prevents accidental sample data seeding in production
- **Clear Purpose**: Immediate understanding of seeder purpose from filename
- **Selective Execution**: Ability to run only essential or sample seeders
- **Maintenance**: Easy identification of seeders that need updates
- **Documentation**: Self-documenting seeder purpose and content

#### Seeder Commands
```bash
# Seed all data
npx sequelize-cli db:seed:all

# Undo all seeders
npx sequelize-cli db:seed:undo:all

# Run specific seeder
npx sequelize-cli db:seed --seed **************-seed-users.js

# Undo specific seeder
npx sequelize-cli db:seed:undo --seed **************-seed-users.js

# With specific environment
npx sequelize-cli db:seed:all --env-file .env.dev
```

### Database Schema Design Principles

#### UUID Primary Keys
All tables use UUID primary keys for enhanced security and distributed system compatibility:
```sql
patient_id UUID PRIMARY KEY DEFAULT gen_random_uuid()
appointment_id UUID PRIMARY KEY DEFAULT gen_random_uuid()
facility_id UUID PRIMARY KEY DEFAULT gen_random_uuid()
```

#### Audit Fields
Every table includes standard audit fields for tracking:
```sql
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
created_by UUID REFERENCES identity(identity_id)
updated_by UUID REFERENCES identity(identity_id)
```

#### Soft Delete Pattern
Critical tables implement soft delete using status fields:
```sql
status INTEGER DEFAULT 0  -- 0: Active, 1: Inactive, 2: Deleted
is_active BOOLEAN DEFAULT true
```

#### Master Data Integration
Reference fields link to master data for consistency:
```sql
-- Instead of ENUM
appointment_status VARCHAR REFERENCES master_data(key)
facility_type VARCHAR REFERENCES master_data(key)

-- Master data structure
master_data (
  group VARCHAR,  -- 'appointment_status', 'facility_type'
  key VARCHAR,    -- 'scheduled', 'completed'
  value VARCHAR   -- 'Scheduled', 'Completed'
)
```

## 🚀 Deployment

### Development Environment
```bash
# Start development server with auto-reload
npm run dev

# Start with specific environment
npm start
```

### Production Deployment

#### Using PM2 (Recommended)
```bash
# Install PM2 globally
npm install -g pm2

# Start application with PM2
pm2 start server.js --node-args="--env-file=.env.dev" --name="caremate_backend"

# Monitor application
pm2 status
pm2 logs caremate_backend
pm2 restart caremate_backend
```

#### Using Docker
```bash
# Build Docker image
docker build -t caremate-api .

# Run container
docker run -d \
  --name caremate-api \
  -p 3001:3001 \
  --env-file .env.dev \
  caremate-api
```

#### Environment-Specific Deployments
```bash
# Local development
npm run dev

# Development server
npm start

# Production (with PM2)
pm2 start ecosystem.config.js --env production
```

## 🔒 Security Features

### Authentication Security
- **JWT Token Validation**: Secure token-based authentication
- **Password Hashing**: bcrypt with salt rounds
- **Session Management**: Secure session handling
- **Multi-Factor Authentication**: Support for MFA (planned)

### API Security
- **Rate Limiting**: Configurable rate limits per endpoint
- **CORS Protection**: Cross-origin request security
- **Helmet Security**: Security headers protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Sequelize ORM protection

### HIPAA Compliance
- **Audit Logging**: Complete activity tracking
- **Data Encryption**: Sensitive data encryption
- **Access Controls**: Role-based access control
- **Secure Communication**: HTTPS enforcement

## 📊 Performance & Monitoring

### Caching Strategy
```javascript
// Cache configuration
CACHE_DRIVER=redis          // Options: redis, memcached, memory
CACHE_TTL=600              // Time-to-live in seconds

// Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### Performance Features
- **Database Read/Write Splitting**: Optimized database access
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Optimized Sequelize queries
- **Response Compression**: Gzip compression
- **Static Asset Caching**: Efficient asset delivery

### Monitoring
- **Winston Logging**: Structured application logging
- **Morgan HTTP Logging**: HTTP request logging
- **Performance Metrics**: Response time tracking
- **Error Tracking**: Comprehensive error logging

## 🛡️ Validation-First Architecture

### Comprehensive Input Validation System

The CareMate API implements a validation-first architecture where every request is thoroughly validated before any business logic execution, ensuring data integrity and security.

**Validation Flow**:
```
Request → Route → Auth Middleware → Validation Middleware → Controller (Business Logic)
```

**Key Components**:

1. **Joi Schema Validation**: Every endpoint uses comprehensive Joi schemas for request validation
2. **Custom Validators**: Specialized validators for master data, UUIDs, and business rules
3. **Error Formatting**: Structured validation error responses with field-specific messages
4. **Async Validation**: Support for database-dependent validation rules
5. **Validation Error Handling**: Graceful error handling with detailed feedback

**Custom Validation Features**:

- **Master Data Validation**: Validates values against centralized master data tables
- **UUID Validation**: Ensures proper UUID format and existence in database
- **Business Rule Validation**: Custom validators for complex business logic
- **Relationship Validation**: Validates foreign key relationships and constraints
- **catchValidationError**: Wrapper that converts database errors to validation errors

**Benefits**:
- **Early Error Detection**: Catches invalid data before processing
- **Consistent Error Format**: Standardized error responses across all endpoints
- **Security**: Prevents injection attacks and malformed data
- **Performance**: Avoids unnecessary processing of invalid requests
- **Developer Experience**: Clear, actionable error messages for API consumers

## ⚡ Transaction Management with catchAsync

### Comprehensive Transaction & Exception Handling

The `catchAsync` helper is a cornerstone of the CareMate API architecture, providing automatic transaction management, exception handling, and audit trail creation without requiring manual try-catch blocks in controllers.

**How catchAsync Works**:

1. **Automatic Transaction Creation**: Creates a new database transaction for each request
2. **Trace Context Generation**: Automatically creates trace context for audit logging on write operations
3. **User Context Injection**: Injects `updated_by` field from authenticated user identity
4. **Exception Handling**: Automatically rolls back transactions on errors and forwards to error middleware
5. **Clean Controller Code**: Eliminates need for manual try-catch blocks in business logic

**Key Features**:
- **Transaction Isolation**: Each request gets its own transaction ensuring data consistency
- **Automatic Rollback**: Any error automatically triggers transaction rollback
- **Audit Trail Integration**: Creates trace context for tracking operations across the system
- **User Attribution**: Automatically tracks which user performed the operation
- **Error Propagation**: Properly forwards errors to Express error handling middleware
- **Optional Transaction Control**: Can disable transaction management when not needed

**Request Flow with catchAsync**:
```
Request → catchAsync → Transaction Creation → Trace Context → User Injection → Controller Logic → Commit/Rollback
```

**Benefits**:
- **Data Integrity**: Ensures all database operations are atomic
- **Clean Code**: Controllers focus on business logic without boilerplate
- **Audit Compliance**: Automatic audit trail creation for regulatory compliance
- **Error Safety**: Guaranteed cleanup on errors prevents data corruption
- **Performance**: Optimized transaction handling with proper resource management

## 🔌 Model Plugins System

The CareMate API uses a sophisticated plugin system that automatically enhances models with additional functionality. These plugins are applied during model initialization and provide enterprise-grade features.

### 1. History Plugin - Complete Audit Trail

**Purpose**: Provides comprehensive audit logging for all model operations with field-level change tracking.

**How it Works**:
- Automatically creates two additional tables for each model: transaction table and history table
- Transaction table records each operation (create/update/delete) with metadata
- History table stores field-level changes with old and new values
- Hooks into Sequelize lifecycle events (afterCreate, afterUpdate, beforeDestroy)
- Excludes sensitive fields from logging based on configuration
- Automatically injects `created_by` and `updated_by` fields for user tracking

**Generated Structure**:
- **Transaction Table**: Records operation type, user, and timestamps
- **History Table**: Records individual field changes with old/new values
- **Automatic Exclusions**: Primary keys, timestamps, and specified sensitive fields

### 2. Pagination Plugin - Efficient Data Retrieval

**Purpose**: Provides standardized pagination functionality for both Sequelize models and plain arrays.

**How it Works**:
- Universal pagination function that works with both database models and arrays
- Automatic detection of input type (Sequelize model vs array)
- Built-in sorting capabilities with configurable sort fields and order
- Limit enforcement (maximum 100 items per page) for performance protection
- Consistent response format across all paginated endpoints

**Features**:
- **Smart Detection**: Automatically handles Sequelize models and plain arrays
- **Flexible Sorting**: Supports custom sort fields and order (ASC/DESC)
- **Performance Limits**: Enforces maximum page size to prevent performance issues
- **Consistent Format**: Returns totalItems, totalPages, currentPage, and data
- **Query Integration**: Seamlessly integrates with Sequelize query options

### 3. Trigger Plugin - Rule Engine Integration

**Purpose**: Automatically triggers business rules and generates events based on model operations.

**How it Works**:
- Integrates with the JSON Rules Engine to evaluate business rules on model changes
- Hooks into Sequelize lifecycle events (afterCreate, afterUpdate, beforeDestroy)
- Validates rule facts against model schema to ensure data integrity
- Supports both individual and bulk operations with optimized processing
- Generates events that are automatically queued to RabbitMQ for asynchronous processing

**Features**:
- **Rule Validation**: Automatically validates rule facts against model attributes
- **Operator Support**: Supports multiple operators (equal, notEqual, greaterThan, lessThan, in, notIn)
- **Bulk Optimization**: Optimized processing for bulk operations with dependency analysis
- **Event Generation**: Automatic event creation and RabbitMQ publishing
- **Performance Monitoring**: Built-in performance tracking and metrics
- **Multiple Events**: Single rule can trigger multiple events with different priorities
- **Changed Field Detection**: Special support for detecting field changes in update operations

### 4. Media Plugin - Image Storage & Processing

**Purpose**: Provides sophisticated media handling with automatic image processing and thumbnail generation.

**How it Works**:
- Scans model attributes for custom MEDIA type fields during initialization
- Automatically creates a separate media storage table for each model
- Converts MEDIA fields to UUID foreign keys that reference the media table
- Processes base64 image data using Sharp library for optimization
- Generates thumbnails automatically for image files (200px width, AVIF format)
- Stores both original and thumbnail versions with metadata

**Features**:
- **Image Detection**: Automatically detects image files from base64 data using file signatures
- **Format Support**: Supports JPEG, PNG, GIF, WEBP, and BMP image formats
- **Thumbnail Generation**: Creates optimized AVIF thumbnails with 50% quality for fast loading
- **Separate Storage**: Uses dedicated media tables with foreign key relationships
- **Custom Getters/Setters**: Provides transparent access to media data through model properties
- **Lifecycle Integration**: Hooks into create and update operations for automatic processing
- **Multiple Media**: Supports multiple media fields per model with individual processing
- **Error Handling**: Graceful handling of invalid media data with comprehensive logging

## 🎯 Rule Engine System

### Comprehensive Rule Engine Flow

The CareMate API implements a sophisticated rule engine using `json-rules-engine` for dynamic business logic that automatically generates events based on model operations.

#### How the Rule Engine Works

**1. Rule Creation & Binding**
- Rules are defined as JSON configurations stored in `/models/rules/` directory
- Each model can have multiple rules bound to specific operations (create, update, delete)
- Rules are applied to models using the Trigger Plugin during model initialization

**2. Rule Structure**
```json
{
  "name": "facility_status_active_rule",
  "conditions": {
    "all": [
      { "fact": "status", "operator": "equal", "value": 0 },
      { "fact": "application", "operator": "equal", "value": "api" }
    ]
  },
  "event": {
    "type": "facilityStatusActive",
    "params": { "message": "The facility status is active" }
  }
}
```

**3. Complete Event Processing Flow**
```
Model Operation (Create/Update/Delete)
    ↓
Trigger Plugin (Applied via Model Hooks)
    ↓
Rules Engine Evaluation (JSON Rules Engine)
    ↓
Fact Building (Instance Data + Context)
    ↓
Rule Condition Evaluation
    ↓
Event Generation (If Conditions Met)
    ↓
Event Storage (Database)
    ↓
RabbitMQ Publishing (Asynchronous Processing)
    ↓
External Event Consumers
```

**4. Rule Engine Features**
- **Dynamic Rule Loading**: Rules loaded from JSON files and validated against model schema
- **Fact Validation**: Automatic validation of rule facts against model attributes
- **Bulk Processing**: Optimized processing for bulk operations with dependency analysis
- **Performance Monitoring**: Built-in performance tracking and metrics
- **Event Prioritization**: Configurable event ordering and queue management
- **Trace Context**: Complete audit trail with trace IDs for event tracking

**5. Event Generation Process**
- Events are automatically created when rule conditions are satisfied
- Each event includes trace ID, model instance data, and custom parameters
- Events are stored in the database and published to RabbitMQ queues
- Multiple events can be generated from a single rule
- Events are processed asynchronously by external consumers

## 📊 Master Data Management

### Centralized Reference Data System

The CareMate API implements a sophisticated master data management system that provides flexible, centralized control over reference values used throughout the application.

**Master Data Concept**:
Master Data serves as a centralized repository for all reference values, lookup tables, and configuration data. Instead of hardcoding enums or status values, the system uses dynamic master data that can be modified at runtime without code changes.

**Key Features**:

1. **Dynamic Reference Values**: All dropdown values, status codes, and lookup data stored centrally
2. **Grouping System**: Related values organized into logical groups (e.g., 'facility_status', 'appointment_type')
3. **Validation Integration**: Automatic validation of model fields against master data
4. **Caching Layer**: High-performance cached retrieval with automatic invalidation
5. **Audit Trail**: Complete tracking of changes to reference data
6. **Internationalization Ready**: Support for multiple languages and localization

**Master Data Structure**:
- **Group**: Category identifier (e.g., 'facility_status')
- **Key**: Internal code value (e.g., 2)
- **Value**: Display value (e.g., 'Active')
- **Sort Order**: Controls display ordering
- **Active Status**: Enables/disables values without deletion

**Benefits**:
- **Flexibility**: Add new reference values without code deployment
- **Consistency**: Centralized management prevents data inconsistencies
- **Performance**: Cached retrieval with 1-hour TTL for optimal performance
- **Maintainability**: Business users can manage reference data independently
- **Scalability**: Supports unlimited groups and values with efficient indexing

## 🗄️ Database Views & Performance Optimization

### Advanced Query Optimization with PostgreSQL Views

The CareMate API leverages PostgreSQL database views to optimize complex, frequently-used queries and provide consistent data presentation across the application.

**Database Views Strategy**:

Database views serve as pre-compiled, optimized queries that combine data from multiple tables with complex joins, aggregations, and master data lookups. This approach significantly improves performance and maintains consistency.

**Key View Types**:

1. **History Views**: Combine transaction and history tables for audit trail display
2. **Appointment Views**: Join appointments with patient, facility, and master data
3. **Patient Views**: Aggregate patient data with identifiers and addresses
4. **Guest Views**: Combine guest information with visit and screening data
5. **Watchlist Views**: Security-focused views with comprehensive tracking

**View Implementation Process**:

1. **Migration Creation**: Database views created through Sequelize migrations
2. **Model Definition**: Sequelize models defined for each view (read-only)
3. **Controller Integration**: Views used in controllers for optimized data retrieval
4. **Caching Layer**: View results cached for frequently accessed data

**Performance Benefits**:

- **Pre-optimized Joins**: Complex joins executed at database level
- **Reduced Network Traffic**: Single query replaces multiple API calls
- **Consistent Data Format**: Standardized data presentation across endpoints
- **Query Plan Optimization**: PostgreSQL optimizes view execution plans
- **Memory Efficiency**: Reduced application-level data processing

**Maintenance Advantages**:

- **Centralized Logic**: Complex query logic maintained in one place
- **Version Control**: View changes tracked through migration system
- **Rollback Support**: Easy rollback of view changes through migrations
- **Documentation**: Views serve as documentation of data relationships

## 🔧 Development Guidelines & Best Practices

### Standardized Development Patterns

The CareMate API follows consistent development patterns that ensure code quality, maintainability, and scalability across the entire application.

#### API Development Pattern

**Standard Controller Structure**:
Every controller follows a consistent pattern using the established middleware stack:

1. **Route Definition**: RESTful routes with comprehensive middleware
2. **Validation**: Joi schema validation for all inputs
3. **Authentication**: Permission-based access control
4. **Transaction Management**: Automatic transaction handling via catchAsync
5. **Business Logic**: Clean, focused controller methods
6. **Response Formatting**: Standardized success/error responses

**Middleware Stack Order**:
```
Route → Authentication → Authorization → Validation → catchAsync(Controller = Business Logic)
```

#### Database Modeling Standards

**Model Enhancement Pattern**:
Every model follows a standardized enhancement pattern with plugins:

1. **Base Model Definition**: Standard Sequelize model with proper field definitions
2. **Plugin Application**: Systematic application of history, trigger, media, and pagination plugins
3. **Validation Integration**: Master data validation and custom business rule validation
4. **Association Definition**: Clear relationship definitions with proper foreign keys
5. **Rule Binding**: Business rules bound to appropriate model operations

**Plugin Application Order**:
1. History Plugin (for audit trail)
2. Trigger Plugin (for business rules)
3. Media Plugin (for file handling)
4. Association definitions

#### Code Quality Standards

- **ESLint**: JavaScript linting with healthcare-specific rules and security checks
- **Prettier**: Consistent code formatting across all files
- **Naming Conventions**: Clear, descriptive naming following healthcare domain patterns
- **File Organization**: Modular, feature-based structure with clear separation of concerns
- **Documentation**: Comprehensive JSDoc comments for all public methods
- **Error Handling**: Consistent error handling using ApiError class and catchAsync
- **Testing**: Unit and integration tests for all critical functionality

## 🚀 Production Deployment Recommendations

### Environment-Specific Configurations

**Development Environment**:
- Use `AUTH_MODE=custom` for easier debugging
- Enable detailed logging with `LOG_LEVEL=debug`
- Use memory cache for faster development cycles

**Production Environment**:
- Use `AUTH_MODE=tyk` for enterprise features
- Configure Redis for caching and session management
- Set up RabbitMQ clustering for high availability
- Enable comprehensive monitoring and alerting

### Security Considerations

- Regularly rotate JWT secrets and API keys
- Monitor authentication failures and suspicious activities
- Implement proper CORS policies for production
- Use HTTPS for all communications
- Regular security audits of dependencies

## 📄 License

This project is proprietary software. All rights reserved.

---

**CareMate API** - Healthcare Management System APIs 
*Built with ❤️ by the OneTalkHub Team*