// File: processor/middlewares/internalMiddleware.js
const logger = require("../config/logger");

/**
 * Custom middleware to log event actions and register them in the event_actions table for processing internal events.
 *
 * @param {Object} options - Configuration options.
 * @param {string} options.functionId - ID of the function being executed.
 * @param {Object} options.models - An object containing your database models.
 * @param {Object} options.models.EventAction - Sequelize model for event_actions.
 *
 * @returns {Object} - An object with before, after, and onError hooks for Middy.
 */
module.exports = function internalMiddleware(options) {
  if (
    !options ||
    !options.functionId ||
    !options.models ||
    !options.models.EventAction
  ) {
    throw new Error(
      "internalMiddleware requires options with properties: functionId and models.EventAction"
    );
  }

  const { EventAction } = options.models;

  return {
    // Called before the handler runs.
    before: async (handler) => {
      const eventId = (handler.event && handler.event.event_id) || null;
      const receivedAt = new Date().toISOString();

      

      logger.info(
        `[BEFORE] Event ID: ${eventId}, Received At: ${receivedAt} – Starting processing for function: ${options.functionId}`
      );
      // Return the promise so Middy waits for it
      await EventAction.create({
        event_id: eventId,
        function_id: options.functionId,
        status: 0, // pending
        message: "Event processing started.",
      });
    },

    // Called after the handler has successfully run.
    after: async (handler) => {
      const eventId = (handler.event && handler.event.event_id) || null;
      const receivedAt = new Date().toISOString();
      logger.info(
        `[AFTER] Event ID: ${eventId}, Received At: ${receivedAt} – Successfully processed internal event in function: ${options.functionId}`
      );
      await EventAction.create({
        event_id: eventId,
        function_id: options.functionId,
        status: 1, // completed
        message: "Event processed successfully.",
      });
    },

    // Called when the handler throws an error.
    onError: async (handler) => {
      try {
      const eventId = (handler.event && handler.event.event_id) || null;
      const receivedAt = new Date().toISOString();
      const errorMessage = handler.error
        ? handler.error.message
        : "Unknown error";
    
      logger.error(
        `[ERROR] Event ID: ${eventId}, Received At: ${receivedAt} – Error in function: ${options.functionId}. Message: ${errorMessage}`
      );
    
        await EventAction.create({
          event_id: eventId,
          function_id: options.functionId,
          status: 2, // failed
          message: errorMessage,
        });
      } catch (dbError) {
        logger.error(
          `[ERROR] Failed to log error event to DB for Event ID: ${eventId}`,
          dbError
        );
      }
    }
    
  };
};
