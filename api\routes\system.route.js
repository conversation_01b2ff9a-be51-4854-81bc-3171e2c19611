const express = require("express");
const validate = require("../middlewares/validate");
const { SystemValidation } = require("../validations");
const { SystemController } = require("../controllers");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Systems
 *   description: System management and retrieval
 */

/**
 * @swagger
 * /system:
 *   get:
 *     summary: Get all systems (paginated)
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *     responses:
 *       200:
 *         description: Paginated list of systems.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 50
 *               totalPages: 5
 *               currentPage: 1
 *               data:
 *                 - system_id: "64b8f0e2d123e4567890abcd"
 *                   name: "PACS System 1"
 *                   system_pacs_id: 101
 *                   description: "Description of PACS System 1"
 *                   created_at: "2025-02-14T12:00:00Z"
 *                   updated_at: "2025-02-14T12:00:00Z"
 */
router.get("/", auth("view_systems"), SystemController.index);

/**
 * @swagger
 * /system/{systemId}:
 *   get:
 *     summary: Get system by ID
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: systemId
 *         required: true
 *         schema:
 *           type: string
 *         description: The system ID
 *     responses:
 *       200:
 *         description: System details.
 *         content:
 *           application/json:
 *             example:
 *               system_id: "64b8f0e2d123e4567890abcd"
 *               name: "PACS System 1"
 *               system_pacs_id: 101
 *               description: "Description of PACS System 1"
 *               created_at: "2025-02-14T12:00:00Z"
 *               updated_at: "2025-02-14T12:00:00Z"
 *       404:
 *         description: System not found
 */
router.get(
  "/:systemId",
  auth("system_details"),
  validate(SystemValidation.system),
  SystemController.show
);

/**
 * @swagger
 * /system:
 *   post:
 *     summary: Create a new system
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "PACS System 1"
 *             system_pacs_id: 101
 *             description: "Description of PACS System 1"
 *     responses:
 *       201:
 *         description: System created successfully
 */
router.post(
  "/",
  auth("create_system"),
  validate(SystemValidation.create),
  SystemController.create
);

/**
 * @swagger
 * /system/{systemId}:
 *   patch:
 *     summary: Update a system
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: systemId
 *         required: true
 *         schema:
 *           type: string
 *         description: The system ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Updated PACS System Name"
 *             system_pacs_id: 102
 *             description: "Updated description"
 *     responses:
 *       200:
 *         description: System updated successfully
 */
router.patch(
  "/:systemId",
  auth("edit_system"),
  validate(SystemValidation.update),
  SystemController.update
);

module.exports = router;
