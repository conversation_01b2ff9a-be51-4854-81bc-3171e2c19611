const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const KioskGroupSetting = sequelize.define(
    "KioskGroupSetting",
    {
      kiosk_group_setting_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      kiosk_group_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "kiosk_group",
          key: "kiosk_group_id",
        },
        onDelete: "CASCADE",
      },
      kiosk_setting_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "kiosk_setting",
          key: "kiosk_setting_id",
        },
        onDelete: "CASCADE",
      },
      config_value: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
    },
    {
      tableName: "kiosk_group_setting",
      timestamps: true,
      underscored: true,
    }
  );

  KioskGroupSetting.associate = (models) => {
    KioskGroupSetting.belongsTo(models.KioskGroup, {
      foreignKey: "kiosk_group_id",
      as: "kiosk_group",
      onDelete: "CASCADE",
    });
    KioskGroupSetting.belongsTo(models.KioskSetting, {
      foreignKey: "kiosk_setting_id",
      as: "kiosk_setting",
      onDelete: "CASCADE",
    });
  };

  history(KioskGroupSetting, sequelize, DataTypes);

  return KioskGroupSetting;
};
