const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const Document = sequelize.define(
    "Document",
    {
      document_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      document_type: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      document_number: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      issue_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      expiration_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      country_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "country",
          key: "country_id",
        },
        onDelete: "SET NULL",
      },
      state_id: {
       type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "state",
          key: "state_id",
        },
        onDelete: "SET NULL",
      },
      other_issuer: {
        type: DataTypes.STRING(200),
        allowNull: true,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      document: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: true,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to identity model",
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "document",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["identity_id"],
        },
        {
          fields: ["document_type"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["document_number"],
        },
      ],
    }
  );

  Document.associate = (models) => {
    Document.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });
    Document.belongsTo(models.Country, {
      foreignKey: "country_id",
      as: "country",
    });
    Document.belongsTo(models.Identity, {
      foreignKey: "state_id",
      as: "state",
    });

    Document.belongsTo(models.MasterData, {
      foreignKey: "document_type",
      targetKey: "key",
      as: "document_type_name",
      constraints: false,
      scope: {
        group: "document_type",
      },
    });

    Document.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "document_status_name",
      constraints: false,
      scope: {
        group: "document_status",
      },
    });
  };

  // Apply plugins
  media(Document, sequelize, DataTypes);
  history(Document, sequelize, DataTypes);

  return Document;
};
