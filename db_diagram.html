<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareMate Complete Database Schema</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .diagram-container {
            width: 100%;
            overflow-x: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fafafa;
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .controls button {
            margin: 0 5px;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .legend h3 {
            margin-top: 0;
            color: #333;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CareMate Complete Database Schema</h1>
        
        <div class="controls">
            <button onclick="zoomIn()">Zoom In</button>
            <button onclick="zoomOut()">Zoom Out</button>
            <button onclick="resetZoom()">Reset Zoom</button>
            <button onclick="downloadSVG()">Download SVG</button>
        </div>
        
        <div class="diagram-container">
            <div id="mermaid-diagram"></div>
        </div>
        
        <div class="legend">
            <h3>Database Schema Overview</h3>
            <div class="legend-item"><strong>Core Modules:</strong></div>
            <div class="legend-item">• Identity & Authentication (Identity, Role, Permission, IdentityVerification, IdentityRole, RolePermission)</div>
            <div class="legend-item">• Facility Management (Facility, Building, Floor, Room, Address)</div>
            <div class="legend-item">• Patient Management (Patient, PatientIdentifier, PatientAddress, PatientGuest)</div>
            <div class="legend-item">• Appointment Management (Appointment, AppointmentGuest, AppointmentGuestCheckin, AppointmentGuestScreening, AppointmentGuestNdaAgreement)</div>
            <div class="legend-item">• Visit Management (Visit, Guest, GuestVisit, VisitGuestCheckin, OutpatientCheckin)</div>
            <div class="legend-item">• Access Control (System, AccessLevel, FacilityAccessLevel, IdentityAccess, Card)</div>
            <div class="legend-item">• Device Management (Device, KioskGroup, DeviceSetting, KioskSetting, KioskGroupSetting)</div>
            <div class="legend-item">• Event Processing (Event, EventTrace, EventAction, EventConfig, Function, ApplicationType)</div>
            <div class="legend-item">• Notification System (Notification, NotificationChannel, NotificationEmail, NotificationText)</div>
            <div class="legend-item">• Document Management (Document, NdaTemplate, NdaAgreement, NdaSignature)</div>
            <div class="legend-item">• Compliance & Logging (HipaaCompliance, HipaaEndpoint, ActivityLog, Watchlist, WatchlistDocument)</div>
            <div class="legend-item">• Configuration (MasterData, GlobalConfiguration, CronConfig, Agent, AgentSetting)</div>
            <div class="legend-item">• Geographic & Localization (Country, State, Language, LanguagePreference, Timezone)</div>
            <div class="legend-item">• Data Processing (Hl7Message, StagingData, TraceAction)</div>
            <div class="legend-item">• Security & Delegation (Token, Delegates)</div>
            <div class="legend-item">• Screening & Watchlist (AppointmentGuestScreeningMatch, Watchlist, WatchlistDocument)</div>
            
            <div class="legend-item" style="margin-top: 15px;"><strong>Key Features:</strong></div>
            <div class="legend-item">• All tables use UUID primary keys for enhanced security</div>
            <div class="legend-item">• Comprehensive audit trails with created_at, updated_at, created_by, updated_by</div>
            <div class="legend-item">• Soft deletes implemented where appropriate</div>
            <div class="legend-item">• Master data driven for flexible configuration</div>
            <div class="legend-item">• HIPAA compliance logging for patient data access</div>
            <div class="legend-item">• Event-driven architecture for processing workflows</div>

            <div class="legend-item" style="margin-top: 20px;"><strong>Entity Relationship Diagram Symbol Key:</strong></div>

            <div class="legend-item" style="margin-top: 10px;"><strong>Relationship Ends:</strong></div>
            <div class="legend-item">• <code>||</code> : exactly one</div>
            <div class="legend-item">• <code>o|</code> or <code>|o</code> : zero or one</div>
            <div class="legend-item">• <code>o{</code> or <code>{o</code> : zero or many</div>
            <div class="legend-item">• <code>|{</code> or <code>{|</code> : one or many</div>

            <div class="legend-item" style="margin-top: 10px;"><strong>Connector:</strong></div>
            <div class="legend-item">• <code>--</code> : plain relationship line</div>

            <div class="legend-item" style="margin-top: 10px;"><strong>Label:</strong></div>
            <div class="legend-item">• Text after a colon (e.g. <code>: processes</code>) names the relationship</div>

            <div class="legend-item" style="margin-top: 10px;"><strong>Example Relationships:</strong></div>
            <div class="legend-item">• <code>Function ||--o{ Appointment : processes</code> - One Function can process zero or many Appointments</div>
            <div class="legend-item">• <code>Patient }o--|| Facility : belongs_to</code> - Zero or one Patient belongs to exactly one Facility</div>
            <div class="legend-item">• <code>Identity ||--o{ IdentityRole : has</code> - One Identity can have zero or many IdentityRoles</div>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ECECFF',
                fontSize: 12
            }
        });

        const diagramDefinition = `
erDiagram
    %% Core Identity and Authentication
    Identity {
        UUID identity_id PK
        UUID facility_id FK
        string first_name
        string last_name
        string email
        string phone
        integer status FK
        datetime created_at
        datetime updated_at
        UUID created_by
        UUID updated_by
    }

    IdentityVerification {
        UUID identity_verification_id PK
        UUID identity_id FK
        string password_hash
        UUID updated_by
        datetime created_at
        datetime updated_at
    }

    Role {
        UUID role_id PK
        string name
        text description
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Permission {
        UUID permission_id PK
        string name
        text description
        datetime created_at
        datetime updated_at
    }

    IdentityRole {
        UUID identity_role_id PK
        UUID identity_id FK
        UUID role_id FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    RolePermission {
        UUID role_permission_id PK
        UUID role_id FK
        UUID permission_id FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Facility Management
    Facility {
        UUID facility_id PK
        string name
        string description
        integer status FK
        integer facility_type FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Building {
        UUID building_id PK
        UUID facility_id FK
        string name
        string address
        integer year_constructed
        string building_code
        integer status FK
        integer type FK
        integer occupancy_type FK
        string phone
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Floor {
        UUID floor_id PK
        UUID facility_id FK
        UUID building_id FK
        string name
        integer floor_number
        integer max_occupancy
        integer occupancy_type FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Room {
        UUID room_id PK
        UUID facility_id FK
        UUID building_id FK
        UUID floor_id FK
        string name
        string room_number
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Address {
        UUID address_id PK
        UUID facility_id FK
        string address_line_1
        string address_line_2
        UUID country_id FK
        UUID state_id FK
        string postal_code
        string map_url
        string region
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Patient Management
    Patient {
        UUID patient_id PK
        UUID function_id FK
        string title
        string first_name
        string middle_name
        string last_name
        string suffix
        string preferred_name
        date birth_date
        integer gender FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    PatientIdentifier {
        UUID patient_identifier_id PK
        UUID patient_id FK
        integer identifier_type FK
        string identifier_value
        integer assigning_authority FK
        date effective_from
        date effective_to
        boolean active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    PatientAddress {
        UUID address_id PK
        UUID patient_id FK
        string address_line_1
        string address_line_2
        string city
        UUID country_id FK
        UUID state_id FK
        string postal_code
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    PatientGuest {
        UUID patient_guest_id PK
        UUID patient_id FK
        string first_name
        string last_name
        string email
        string phone
        string organization
        integer guest_type FK
        integer relationship_type FK
        integer relationship_status FK
        json image
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Appointment Management
    Appointment {
        UUID appointment_id PK
        UUID function_id FK
        UUID patient_id FK
        UUID facility_id FK
        datetime appointment_date
        integer type FK
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    AppointmentGuest {
        UUID appointment_guest_id PK
        UUID appointment_id FK
        UUID patient_guest_id FK
        UUID facility_id FK
        integer screening FK
        integer facility FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Visit Management
    Visit {
        UUID visit_id PK
        UUID facility_id FK
        string name
        text description
        integer type FK
        integer category FK
        integer repeat_visit FK
        datetime start_date
        datetime end_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Guest {
        UUID guest_id PK
        string first_name
        string last_name
        string email
        string phone
        string organization
        json image
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    GuestVisit {
        UUID guest_visit_id PK
        UUID guest_id FK
        UUID visit_id FK
        integer guest_status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Access Control
    System {
        UUID system_id PK
        string name
        integer system_pacs_id
        text description
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    AccessLevel {
        UUID access_level_id PK
        UUID system_id FK
        UUID facility_id FK
        UUID card_id FK
        string name
        string pacs_area_name
        text description
        string pacs_access_level_id
        integer status FK
        boolean online
        boolean requestable_self_service
        string access_level_type
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Card {
        UUID card_id PK
        UUID identity_id FK
        string card_number
        integer card_format FK
        integer template FK
        integer status FK
        datetime expiry_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Device Management
    Device {
        UUID device_id PK
        UUID kiosk_group_id FK
        UUID facility_id FK
        UUID facility_building_id FK
        UUID facility_floor_id FK
        UUID facility_room_id FK
        string name
        string device_type
        string ip_address
        string mac_address
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    KioskGroup {
        UUID kiosk_group_id PK
        string name
        text description
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Event and Processing
    Event {
        UUID event_id PK
        UUID trace_id FK
        string parent_id
        string child_id
        string event_type
        json params
        integer order
        string queue
        datetime created_at
    }

    EventTrace {
        UUID trace_id PK
        string endpoint
        UUID function_id FK
        datetime created_at
    }

    Function {
        UUID function_id PK
        UUID application_type_id FK
        string queue
        string type
        string name
        string display_name
        text description
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    ApplicationType {
        UUID application_type_id PK
        string name
        string display_name
        string description
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    EventConfig {
        UUID event_config_id PK
        string event_name
        string event
        string queue
        integer order
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    EventAction {
        UUID event_action_id PK
        UUID event_id FK
        UUID function_id FK
        text message
        integer status FK
        datetime created_at
    }

    Agent {
        UUID agent_id PK
        string name
        string type
        string display_name
        string description
        string source
        string queue
        string cron
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    AgentSetting {
        UUID agent_setting_id PK
        UUID agent_id FK
        string name
        string value
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    GlobalConfiguration {
        UUID global_configuration_id PK
        string name
        string display_name
        string value
        text description
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    CronConfig {
        UUID cron_config_id PK
        string name
        string display_name
        string description
        string schedule
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    NotificationChannel {
        UUID notification_channel_id PK
        UUID notification_id FK
        string channel
        UUID notification_child_id FK
        string status
        datetime created_at
        datetime updated_at
    }

    NotificationEmail {
        UUID notification_email_id PK
        string name
        string subject
        text template
        string receiver_column
        string status
        string language
        datetime created_at
        datetime updated_at
    }

    NotificationText {
        UUID notification_text_id PK
        string name
        text template
        string receiver_column
        string status
        string language
        datetime created_at
        datetime updated_at
    }

    Language {
        UUID language_id PK
        string name
        string code
        boolean default
        boolean status
        json addons
        UUID updated_by
    }

    LanguagePreference {
        UUID language_preference_id PK
        UUID identity_id FK
        UUID language_id FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Timezone {
        UUID timezone_id PK
        UUID country_id FK
        string code
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    HipaaCompliance {
        UUID id PK
        UUID identity_id FK
        UUID patient_id FK
        string endpoint
        datetime action_time
        datetime created_at
        datetime updated_at
    }

    HipaaEndpoint {
        UUID hipaa_endpoint_id PK
        string endpoint
        string method
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    ActivityLog {
        UUID id PK
        UUID identity_id FK
        string action
        json metadata
        datetime created_at
        datetime updated_at
    }

    NdaTemplate {
        UUID nda_template_id PK
        string name
        integer version
        text document_url
        string jurisdiction
        datetime created_at
        datetime updated_at
    }

    NdaAgreement {
        UUID nda_agreement_id PK
        UUID identity_id FK
        UUID nda_template_id FK
        date effective_date
        date expiration_date
        integer status FK
        datetime signed_at
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    NdaSignature {
        UUID nda_signature_id PK
        UUID nda_agreement_id FK
        json signature_data
        string ip_address
        datetime created_at
        datetime updated_at
    }

    DeviceSetting {
        UUID device_setting_id PK
        UUID nda_template_id FK
        UUID device_id FK
        boolean shownda
        boolean showoutpatient
        boolean showexpeditecheckin
        boolean showwalkinguest
        datetime created_at
        datetime updated_at
    }

    KioskSetting {
        UUID kiosk_setting_id PK
        UUID device_id FK
        string name
        string value
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    KioskGroupSetting {
        UUID kiosk_group_setting_id PK
        UUID kiosk_group_id FK
        string name
        string value
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    FacilityAccessLevel {
        UUID facility_access_level_id PK
        UUID access_level_id FK
        UUID facility_id FK
        UUID building_id FK
        UUID floor_id FK
        UUID room_id FK
        text entry_restrictions
        text access_protocol
        boolean requestable_guest
        boolean default_access_guest
        boolean default_access_identity
        array identity_type
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    IdentityAccess {
        UUID identity_access_id PK
        UUID identity_id FK
        UUID access_level_id FK
        datetime granted_at
        datetime expires_at
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Delegates {
        UUID delegate_id PK
        UUID identity_id FK
        UUID delegate_identity_id FK
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Token {
        UUID token_id PK
        UUID identity_id FK
        string token_hash
        string type
        datetime expires_at
        boolean is_used
        datetime created_at
        datetime updated_at
    }

    TraceAction {
        UUID trace_action_id PK
        UUID trace_id FK
        string action
        json metadata
        datetime created_at
    }

    Hl7Message {
        UUID hl7_message_id PK
        string message_control_id
        string message_type
        string trigger_event
        text raw_message
        json parsed_data
        integer status FK
        datetime processed_at
        datetime created_at
        datetime updated_at
    }

    StagingData {
        UUID staging_data_id PK
        string source_type
        string source_id
        json raw_data
        json processed_data
        integer status FK
        datetime processed_at
        datetime created_at
        datetime updated_at
    }

    OutpatientCheckin {
        UUID outpatient_checkin_id PK
        UUID patient_id FK
        UUID appointment_id FK
        UUID device_id FK
        datetime checkin_time
        integer status FK
        datetime created_at
        datetime updated_at
    }

    AppointmentGuestNdaAgreement {
        UUID appointment_guest_nda_agreement_id PK
        UUID appointment_guest_id FK
        UUID nda_agreement_id FK
        datetime signed_at
        datetime created_at
        datetime updated_at
    }

    AppointmentGuestScreening {
        UUID appointment_guest_screening_id PK
        UUID appointment_guest_id FK
        json screening_data
        integer status FK
        datetime screened_at
        datetime created_at
        datetime updated_at
    }

    AppointmentGuestScreeningMatch {
        UUID appointment_guest_screening_match_id PK
        UUID appointment_guest_screening_id FK
        UUID watchlist_id FK
        decimal match_score
        integer status FK
        datetime created_at
        datetime updated_at
    }

    VisitGuestCheckin {
        UUID visit_guest_checkin_id PK
        UUID guest_visit_id FK
        UUID device_id FK
        datetime checkin_time
        datetime checkout_time
        integer status FK
        datetime created_at
        datetime updated_at
    }

    WatchlistDocument {
        UUID watchlist_document_id PK
        UUID watchlist_id FK
        UUID document_id FK
        datetime created_at
        datetime updated_at
    }

    %% Configuration and Master Data
    MasterData {
        UUID master_data_id PK
        string group
        integer key
        string value
        datetime created_at
        datetime updated_at
        datetime deleted_at
        UUID updated_by
    }

    %% Geographic Data
    Country {
        UUID country_id PK
        string name
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    State {
        UUID state_id PK
        UUID country_id FK
        string name
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Watchlist Management
    Watchlist {
        UUID watchlist_id PK
        string first_name
        string last_name
        string email
        string phone
        integer status FK
        integer reason FK
        date expiry_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Notification System
    Notification {
        UUID notification_id PK
        string name
        text description
        integer status
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Document Management
    Document {
        UUID document_id PK
        UUID identity_id FK
        UUID country_id FK
        UUID state_id FK
        string document_number
        integer document_type FK
        integer status FK
        date expiry_date
        json document_image
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Training Management
    Training {
        UUID training_id PK
        UUID identity_id FK
        string title
        text description
        integer course_type FK
        integer status FK
        integer recurrence FK
        date completion_date
        date expiry_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Vehicle Management
    Vehicle {
        UUID vehicle_id PK
        UUID identity_id FK
        string make
        string model
        string year
        string license_plate
        string color
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Relationships
    Identity ||--o{ IdentityVerification : has
    Identity ||--o{ IdentityRole : has
    Identity }o--|| Facility : belongs_to
    Role ||--o{ IdentityRole : has
    Role ||--o{ RolePermission : has
    Permission ||--o{ RolePermission : has
    Facility ||--o{ Building : contains
    Facility ||--o| Address : has
    Building ||--o{ Floor : contains
    Building }o--|| Facility : belongs_to
    Floor ||--o{ Room : contains
    Floor }o--|| Building : belongs_to
    Floor }o--|| Facility : belongs_to
    Room }o--|| Floor : belongs_to
    Room }o--|| Building : belongs_to
    Room }o--|| Facility : belongs_to
    Patient ||--o{ Appointment : has
    Patient ||--o| PatientIdentifier : has
    Patient ||--o| PatientAddress : has
    Patient ||--o{ PatientGuest : has
    Patient }o--o| Function : processed_by
    Appointment }o--|| Patient : for
    Appointment }o--|| Facility : at
    Appointment }o--o| Function : processed_by
    Appointment ||--o{ AppointmentGuest : has
    AppointmentGuest }o--|| Appointment : belongs_to
    AppointmentGuest }o--|| PatientGuest : for
    AppointmentGuest }o--|| Facility : at
    Visit }o--|| Facility : at
    Visit ||--o{ GuestVisit : has
    Guest ||--o{ GuestVisit : participates_in
    GuestVisit }o--|| Visit : belongs_to
    GuestVisit }o--|| Guest : for
    System ||--o{ AccessLevel : defines
    AccessLevel }o--|| System : belongs_to
    AccessLevel }o--|| Facility : for
    AccessLevel }o--o| Card : uses
    Card }o--|| Identity : belongs_to
    Device }o--|| KioskGroup : belongs_to
    Device }o--o| Facility : located_at
    Device }o--o| Building : located_in
    Device }o--o| Floor : located_on
    Device }o--o| Room : located_in
    KioskGroup ||--o{ Device : contains
    EventTrace ||--o{ Event : contains
    EventTrace }o--o| Function : triggered_by
    Event }o--|| EventTrace : belongs_to
    Function ||--o{ EventTrace : triggers
    Function ||--o{ Patient : processes
    Function ||--o{ Appointment : processes
    Document }o--|| Identity : belongs_to
    Document }o--o| Country : issued_in
    Document }o--o| State : issued_in
    Training }o--|| Identity : assigned_to
    Vehicle }o--|| Identity : owned_by
    Country ||--o{ State : contains
    State }o--|| Country : belongs_to
    Country ||--o{ Address : used_in
    Country ||--o{ PatientAddress : used_in
    Country ||--o{ Document : issued_in
    Country ||--o{ Timezone : contains
    State ||--o{ Address : used_in
    State ||--o{ PatientAddress : used_in
    State ||--o{ Document : issued_in
    ApplicationType ||--o{ Function : defines
    Function }o--|| ApplicationType : belongs_to
    Function ||--o{ EventTrace : triggers
    Function ||--o{ Patient : processes
    Function ||--o{ Appointment : processes
    Function ||--o{ EventAction : executes
    EventAction }o--|| Function : executed_by
    EventAction }o--|| Event : belongs_to
    Event ||--o{ EventAction : has
    Agent ||--o{ AgentSetting : has
    AgentSetting }o--|| Agent : belongs_to
    Notification ||--o{ NotificationChannel : has
    NotificationChannel }o--|| Notification : belongs_to
    NotificationChannel ||--o{ NotificationEmail : has
    NotificationChannel ||--o{ NotificationText : has
    Language ||--o{ LanguagePreference : has
    LanguagePreference }o--|| Language : for
    LanguagePreference }o--|| Identity : belongs_to
    Identity ||--o{ LanguagePreference : has
    Timezone }o--|| Country : belongs_to
    Identity ||--o{ HipaaCompliance : has
    Patient ||--o{ HipaaCompliance : accessed_by
    HipaaCompliance }o--|| Identity : performed_by
    HipaaCompliance }o--|| Patient : for
    Identity ||--o{ ActivityLog : has
    ActivityLog }o--|| Identity : performed_by
    NdaTemplate ||--o{ NdaAgreement : used_in
    NdaTemplate ||--o{ DeviceSetting : configured_in
    NdaAgreement }o--|| NdaTemplate : uses
    NdaAgreement }o--|| Identity : signed_by
    NdaAgreement ||--o{ NdaSignature : has
    NdaAgreement ||--o{ AppointmentGuestNdaAgreement : used_in
    NdaSignature }o--|| NdaAgreement : for
    Identity ||--o{ NdaAgreement : signs
    Device ||--o{ DeviceSetting : has
    Device ||--o{ KioskSetting : has
    Device ||--o{ VisitGuestCheckin : used_for
    Device ||--o{ OutpatientCheckin : used_for
    DeviceSetting }o--|| Device : belongs_to
    DeviceSetting }o--|| NdaTemplate : uses
    KioskSetting }o--|| Device : belongs_to
    KioskGroup ||--o{ KioskGroupSetting : has
    KioskGroupSetting }o--|| KioskGroup : belongs_to
    AccessLevel ||--o{ FacilityAccessLevel : configured_in
    AccessLevel ||--o{ IdentityAccess : granted_to
    FacilityAccessLevel }o--|| AccessLevel : configures
    FacilityAccessLevel }o--|| Facility : for
    FacilityAccessLevel }o--o| Building : in
    FacilityAccessLevel }o--o| Floor : on
    FacilityAccessLevel }o--o| Room : in
    IdentityAccess }o--|| Identity : granted_to
    IdentityAccess }o--|| AccessLevel : for
    Identity ||--o{ IdentityAccess : has
    Identity ||--o{ Delegates : delegates_to
    Identity ||--o{ Delegates : delegated_by
    Delegates }o--|| Identity : delegator
    Delegates }o--|| Identity : delegate
    Identity ||--o{ Token : has
    Token }o--|| Identity : belongs_to
    EventTrace ||--o{ TraceAction : has
    TraceAction }o--|| EventTrace : belongs_to
    Patient ||--o{ OutpatientCheckin : has
    Appointment ||--o{ OutpatientCheckin : for
    OutpatientCheckin }o--|| Patient : for
    OutpatientCheckin }o--|| Appointment : for
    OutpatientCheckin }o--|| Device : performed_on
    AppointmentGuest ||--o{ AppointmentGuestNdaAgreement : has
    AppointmentGuestNdaAgreement }o--|| AppointmentGuest : for
    AppointmentGuestNdaAgreement }o--|| NdaAgreement : uses
    AppointmentGuest ||--o{ AppointmentGuestScreening : has
    AppointmentGuestScreening }o--|| AppointmentGuest : for
    AppointmentGuestScreening ||--o{ AppointmentGuestScreeningMatch : has
    AppointmentGuestScreeningMatch }o--|| AppointmentGuestScreening : for
    AppointmentGuestScreeningMatch }o--|| Watchlist : matches
    Watchlist ||--o{ AppointmentGuestScreeningMatch : matched_in
    Watchlist ||--o{ WatchlistDocument : has
    WatchlistDocument }o--|| Watchlist : belongs_to
    WatchlistDocument }o--|| Document : references
    Document ||--o{ WatchlistDocument : used_in
    GuestVisit ||--o{ VisitGuestCheckin : has
    VisitGuestCheckin }o--|| GuestVisit : for
    VisitGuestCheckin }o--|| Device : performed_on
        `;

        // Render the diagram
        mermaid.render('mermaid-svg', diagramDefinition).then(({svg}) => {
            document.getElementById('mermaid-diagram').innerHTML = svg;
        });

        // Zoom functionality
        let currentZoom = 1;
        
        function zoomIn() {
            currentZoom += 0.1;
            applyZoom();
        }
        
        function zoomOut() {
            currentZoom = Math.max(0.1, currentZoom - 0.1);
            applyZoom();
        }
        
        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }
        
        function applyZoom() {
            const diagram = document.getElementById('mermaid-diagram');
            diagram.style.transform = `scale(${currentZoom})`;
            diagram.style.transformOrigin = 'top left';
        }
        
        function downloadSVG() {
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], {type: 'image/svg+xml'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'caremate-database-schema.svg';
                a.click();
                URL.revokeObjectURL(url);
            }
        }
    </script>
</body>
</html>
