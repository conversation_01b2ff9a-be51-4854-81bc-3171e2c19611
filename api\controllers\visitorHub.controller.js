
const { Identity } = require('../models');
const { Op } = require('sequelize');

/**
 * Search Host by Name and Facility
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 * @param {import('express').NextFunction} next
 */
exports.searchHost = async (req, res, next) => {
  try {
    const { name = '', facility_id } = req.query;
    const where = {
      facility_id,
    };
    if (name && name.trim()) {
      where[Op.or] = [
        { first_name: { [Op.iLike]: `%${name}%` } },
        { last_name: { [Op.iLike]: `%${name}%` } },
      ];
    }
    const hosts = await Identity.findAll({
      where,
      attributes: [
        'identity_id',
        'first_name',
        'last_name',
        'job_title',
        'eid',
        'status',
        'start_date',
        'end_date',
        'image',
      ],
    });
    return res.json(hosts);
  } catch (err) {
    next(err);
  }
};
