"use strict";
const { v4: uuidv4 } = require("uuid");
const logger = require("../config/logger");

function pushMasterData(arr, groupName, values, existingValuesSet) {
  // ensure we have an array (masterGroups uses objects)
  const items = Array.isArray(values) ? values : Object.values(values);
  items.forEach((val, index) => {
    const key = `${groupName}|${val}`;
    if (!existingValuesSet.has(key)) {
      arr.push({
        master_data_id: uuidv4(),
        group: groupName,
        key: index,
        value: val,
        created_at: new Date(),
        updated_at: new Date(),
        updated_by: '00000000-0000-0000-0000-000000000000',
      });
    }
  });
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const masterData = [];

    const masterGroups = {
      facility_status: { 0: "Active", 1: "Inactive" },
      facility_type: {
        0: "Industrial",
        1: "Commercial",
        2: "Residential",
        3: "Institutional",
        4: "Recreational",
      },
      building_status: { 0: "Active", 1: "Inactive" },
      building_type: {
        0: "Industrial",
        1: "Commercial",
        2: "Residential",
        3: "Institutional",
        4: "Recreational",
      },
      building_occupancy_type: {
        0: "Assembly Occupancy",
        1: "Business Occupancy",
        2: "Educational Occupancy",
        3: "Factory And Industrial",
        4: "High Hazard",
        5: "Institutional Occupancy",
        6: "Mercantile Occupancy",
        7: "Residential Occupancy",
        8: "Storage Occupancy",
        9: "Utility And Miscellaneous",
      },
      floor_status: { 0: "Active", 1: "Inactive" },
      floor_occupancy_type: {
        0: "Residential",
        1: "Office Space",
        2: "Retail",
        3: "Warehouse",
        4: "Parking",
        5: "Recreational",
        6: "Mechanical",
        7: "Conference",
        8: "Food Court",
        9: "Medical",
      },
      room_status: { 0: "Active", 1: "Inactive" },
      watchlist_status: { 0: "Active", 1: "Inactive" },
      watchlist_reason: { 0: "Reason 1", 1: "Reason 2" },
      risk_level: { 0: "Low", 1: "Medium", 2: "High", 3: "Critical" },
      appointment_type: { 0: "Inpatient", 1: "Outpatient" },
      appointment_status: {
        0: "Pending",
        1: "Admitted",
        2: "Discharged",
        3: "Arrived",
        4: "Departed",
        5: "Cancelled",
      },
      patient_gender: { 0: "Male", 1: "Female", 2: "Other" },
      patient_confidentiality_code: { 0: "Normal", 1: "Restricted" },
      patient_marital_status: {
        0: "Single",
        1: "Married",
        2: "Divorced",
        3: "Widowed",
      },
      patient_preferred_language: {
        0: "en-US",
        1: "es-MX",
        2: "fr-FR",
        3: "de-DE",
        4: "zh-CN",
      },
      patient_identifier_identifier_type: { 0: "MRN", 1: "SSN", 2: "DL" },
      patient_identifier_assigning_authority: { 0: "HospitalA", 1: "SSA" },
      appointment_guest_status: {
        0: "Pending Check-In",
        1: "Checked-In",
        2: "Checked-Out",
        3: "Check-In Denied",
        4: "Registered",
      },
      patient_guest_guest_type: {
        0: "Guest",
        1: "Friend Family",
        2: "Denied Guest",
      },
      patient_guest_relation_type: {
        0: "Spouse",
        1: "Child",
        2: "Friend",
        3: "Emergency Contact",
      },
      patient_guest_relationship_status: {
        0: "Active",
        1: "Historical",
        2: "Estranged",
      },
      event_actions_status: { 0: "Pending", 1: "Completed", 2: "Failed" },
      trace_actions_status: { 0: "Pending", 1: "Completed", 2: "Failed" },
      guest_screening:{
        0: "Screening Pass",
        1: "Screening Failed",
        2: "Override Revoked"

      },
      override_screening:{
        0:"yes",
        1:"no"
      },
      screening_match_type:{
        0:"Denied Guest",
        1:"Watchlist"
      },
      card_format:{
        0:"HIDICLASS",
        1:"HID"
      },
      card_status:{
        0:"Active",
        1:"Inactive",
        2:"Pending Assignment",
        3:"Pending Activation"
      },
      card_template:{
        0:"Employee",
        1:"Contractor"
      },
      identity_status:{
        0:"Active",
        1:"Terminated",
        2:"Suspended"
      },
      identity_type:{
        0:"COS",
        1:"EMP"
      },
      access_level_status:{
        0:"Active",
        1:"Inactive"
      },
      access_level_type:{
        0:"common",
        1:"datacenter",
        2:"restricted",
        3:"highsecurity",
        4:"visitor",
        5:"emergency",
        6:"classified",
        7:"outdoor",
      },
      identity_access_status:{
        0:"Assignment",
        1:"Pending",
      },
      nda_agreement_status: {
        0: "Pending",
        1: "Signed",
        2: "Expired",
        3: "Cancelled",
      },
      signer_role: {
        0: "User",
        1: "Admin",
        2: "Legal Reviewer",
        3: "External Party",
      },
      signature_method: {
        0: "E-Signature",
        1: "Manual Upload",
        2: "Digital Signature",
        3: "Wet Signature",
      },
      document_type: {
        0: "Passport",
        1: "DrivingLicense",
        2: "Other",
      },
      document_status: {
        0: "Active",
        1: "Inactive",
        2: "Expired",
      },
      training_status: {
        0: "Pass",
        1: "Fail",
        2: "Pending",
      },
      delegate_status: {
        0: "Active",
        1: "Inactive",
      },
      course_type: {
        0: "Mandatory",
        1: "Optional",
      },
      recurrence: {
        0: "Annual",
        1: "Biennial",
        2: "Triennial",
      },
      guest_status: {
        0: "Invited",
        1: "Checked In",
        2: "Checked Out",
        3: "Check In Denied",
      },
      visit_type: {
        0: "Schedulded",
        1: "Walkin",
      },
      visit_category: {
        0: "Interview",
        1: "Personal Visit",
        2: "Customer Visit",
      },
      repeat_visit: {
        0: "Daily",
        1: "Weekly",
        2: "Monthly",
        3: "Yearly",
        4: "Never",
      },
      check_in_instruction: {
        0: "Self Check-In",
        1: "Host Check-In",
        2: "Escort Check-In",
      },
      remind_me: {
        0: "15 Minutes Before",
        1: "2 Days Before",
        2: "1 Week Before",
        3: "2 Weeks Before",
        4: "1 Month Before",
        5: "2 Months Before",
        6: "Never",
      },
      visit_status: {
        0: "Pending",
        1: "In Progress",
        2: "Completed",
        3: "Cancelled",
      },


    };

    const groupNames = Object.keys(masterGroups);

    const [existingRecords] = await queryInterface.sequelize.query(
      `SELECT "group", "value" FROM master_data WHERE "group" IN (:groupNames)`,
      { replacements: { groupNames } }
    );

    const existingValuesSet = new Set(
      existingRecords.map((rec) => `${rec.group}|${rec.value}`)
    );

    for (const [group, values] of Object.entries(masterGroups)) {
      pushMasterData(masterData, group, values, existingValuesSet);
    }

    if (masterData.length > 0) {
      try {
        await queryInterface.bulkInsert("master_data", masterData, {});
      } catch (error) {
        if (error.code === "23505") {
          logger.error(
            "Duplicate entry detected. Skipping insertion.",
            error.detail
          );
        } else {
          logger.error("Error inserting master data:", error);
        }
      }
    } else {
      logger.info("No new master data to insert. All entries already exist.");
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("master_data", {
      group: {
        [Sequelize.Op.in]: [
          "facility_status",
          "facility_type",
          "building_status",
          "building_type",
          "building_occupancy_type",
          "floor_status",
          "floor_occupancy_type",
          "room_status",

          "watchlist_status",
          "watchlist_reason",
          "risk_level",

          "appointment_type",
          "appointment_status",

          "patient_gender",
          "patient_confidentiality_code",
          "patient_marital_status",
          "patient_preferred_language",
          "patient_identifier_identifier_type",
          "patient_identifier_assigning_authority",

          "appointment_guest_status",
          "patient_guest_guest_type",
          "patient_guest_relation_type",
          "patient_guest_relationship_status",
          "guest_screening",
          "override_screening",
          "screening_match_type",

          "event_action_status",
          "trace_action_status",

          "identity_status",
          "identity_type",
          "card_format",
          "card_template",
          "access_level_status",
          "access_level_type",
          "identity_access_status",
          "nda_agreement_status",
          "signer_role",
          "signature_method",
          "document_type",
          "document_status",
          "training_status",
          "course_type",
          "recurrence",
          "delegate_status",
          "guest_status",
          "visit_type",
          "visit_category",
          "repeat_visit",
          "check_in_instruction",
          "remind_me",
          "visit_status",


           
        ],
      },
    });
  },
};
