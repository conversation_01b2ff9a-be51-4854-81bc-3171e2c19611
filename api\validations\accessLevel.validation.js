const Joi = require("joi");
const { unique, exists, existsMasterData } = require("./custom.validation");
const { Status, AccessLevelType } = require("../config/attributes");

/**
 * Schema for creating a new access level.
 */
const create = {
  body: Joi.object().keys({
    name: Joi.string().max(100).required().label("Name"),
    description: Joi.string().optional().allow(null, "").label("Description"),
    pacs_access_level_id: Joi.string()
      .max(50)
      .optional()
      .external(unique("AccessLevel", "pacs_access_level_id"))
      .label("PACS Access Level ID"),
    system_id: Joi.string()
      .guid({ version: ["uuidv4"] })
      .optional()
      .external(exists("System", "system_id"))
      .label("System ID"),
    status: Joi.number().integer().external(existsMasterData("access_level_status")).optional(),
    access_level_type: Joi.number().integer().external(existsMasterData("access_level_type")).optional(),
    facility_id: Joi.string()
      .guid({ version: ["uuidv4"] })
      .optional()
      .external(exists("Facility", "facility_id"))
      .label("Facility ID"),
  }),
};

/**
 * Schema for updating an existing access level.
 */
const update = {
  params: Joi.object().keys({
    accessLevelId: Joi.string()
      .guid({ version: ["uuidv4"] })
      .required()
      .external(exists("AccessLevel", "access_level_id"))
      .label("Access Level ID"),
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().max(100).optional().label("Name"),
      description: Joi.string().optional().allow(null, "").label("Description"),
      pacs_access_level_id: Joi.string()
        .max(50)
        .optional()
        .external(unique("AccessLevel", "pacs_access_level_id"))
        .label("PACS Access Level ID"),
      system_id: Joi.string()
        .guid({ version: ["uuidv4"] })
        .optional()
        .external(exists("System", "system_id"))
        .label("System ID"),
      status: Joi.number().integer().external(existsMasterData("access_level_status")).optional(),
      access_level_type: Joi.number().integer().external(existsMasterData("access_level_type")).optional(),
      facility_id: Joi.string()
        .guid({ version: ["uuidv4"] })
        .optional()
        .external(exists("Facility", "facility_id"))
        .label("Facility ID"),
    })
    .min(1),
};

/**
 * Schema for retrieving an access level by ID.
 */
const accessLevel = {
  params: Joi.object().keys({
    accessLevelId: Joi.string()
      .guid({ version: ["uuidv4"] })
      .required()
      .external(exists("AccessLevel", "access_level_id"))
      .label("Access Level ID"),
  }),
};

/**
 * Schema for updating access level status.
 */
const status = {
  params: Joi.object().keys({
    accessLevelId: Joi.string()
      .guid({ version: ["uuidv4"] })
      .required()
      .external(exists("AccessLevel", "access_level_id"))
      .label("Access Level ID"),
  }),
  body: Joi.object().keys({
    status: Joi.number().integer().external(existsMasterData("access_level_status")).optional(),
  }),
};

module.exports = {
  create,
  update,
  accessLevel,
  status,
};
