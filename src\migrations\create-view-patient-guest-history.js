'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_patient_guest_history AS
      SELECT
        h.created_at AS effective_date,
        h.patient_guest_history_id,
        h.column_name AS field_changes,
        h.old_value,
        h.new_value,
        t.operation AS event_type,
        t.patient_guest_id AS patient_guest_id,
        t.updated_by AS modified_by
      FROM patient_guest_history h
      JOIN patient_guest_transaction t
        ON h.patient_guest_transaction_id = t.patient_guest_transaction_id;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS view_patient_guest_history;`
    );
  },
};
