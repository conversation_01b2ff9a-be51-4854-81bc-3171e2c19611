const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const Room = sequelize.define(
    "Room",
    {
      room_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "CASCADE",
      },
      building_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "building",
          key: "building_id",
        },
        onDelete: "CASCADE",
      },
      floor_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "floor",
          key: "floor_id",
        },
      },
      room_number: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      max_occupancy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      area: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      primary_contact_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      primary_contact_number: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isNumeric: true,
        },
      },
      primary_contact_email: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "room",
      timestamps: true,
      underscored: true,
    }
  );

  Room.associate = (models) => {
    Room.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
      onDelete: "CASCADE",
    });
    Room.belongsTo(models.Building, {
      foreignKey: "building_id",
      as: "building",
      onDelete: "CASCADE",
    });
    Room.belongsTo(models.Floor, {
      foreignKey: "floor_id",
      as: "floor",
      onDelete: "CASCADE",
    });
    Room.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "room_status_name",
      constraints: false,
      scope: { group: "room_status" },
    });
    
  };

  history(Room, sequelize, DataTypes);

  return Room;
};
