const { HipaaCompliance , HipaaEndpoint} = require("../models");

module.exports = function hipaaLogger(req, res, next) {
  const identityId = req.user?.identity_id || req.identity?.identity_id;
  const endpoint = req.originalUrl;

  HipaaEndpoint.findOrCreate({
    where: { endpoint },
    defaults: { endpoint }
  }).catch((err) => {
    console.error("[HIPAA] Failed to log endpoint:", err);
  });

  if (!identityId) return next();

  const originalJson = res.json;

  res.json = async function (body) {
    let patientIds = [];

    try {
      if (Array.isArray(body)) {
        patientIds = body.map(item => item.patient_id).filter(Boolean);
      } else if (body.patient_id) {
        patientIds = [body.patient_id];
      } else if (typeof body === 'object' && Array.isArray(body.data)) {
        patientIds = body.data.map(item => item.patient_id).filter(Boolean);
      } else if (body.data?.patient_id) {
        patientIds = [body.data.patient_id];
      }

      for (const pid of patientIds) {
        await HipaaCompliance.create({
          identity_id: identityId,
          patient_id: pid,
          endpoint,
        });
      }
    } catch (err) {
      console.error("[HIPAA LOGGER] Logging failed:", err);
    }

    return originalJson.call(this, body);
  };

  next();
};

