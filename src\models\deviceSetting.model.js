const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const DeviceSetting = sequelize.define(
    "DeviceSetting",
    {
      device_setting_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      nda_template_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_template",
          key: "nda_template_id",
        },
        onDelete: "CASCADE",
      },
      device_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "device",
          key: "device_id",
        },
        onDelete: "CASCADE",
      },
      shownda: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      showoutpatient: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      showexpeditecheckin: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      showwalkinguest: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: "device_setting",
      timestamps: true,
      underscored: true,
    }
  );

  DeviceSetting.associate = (models) => {
    DeviceSetting.belongsTo(models.Device, {
      foreignKey: "device_id",
      as: "device",
      onDelete: "CASCADE",
    });
    DeviceSetting.belongsTo(models.NdaTemplate, {
      foreignKey: "nda_template_id",
      as: "nda_template",
    });
    // NDA association can be added here if NDA model exists
  };

  history(DeviceSetting, sequelize, DataTypes);

  return DeviceSetting;
};
