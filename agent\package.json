{"name": "agent", "version": "1.0.0", "description": "To make it easy for you to get started with GitLab, here's a list of recommended next steps.", "repository": {"type": "git", "url": "https://darshil:<EMAIL>/care/care-agent"}, "license": "ISC", "author": "<PERSON><PERSON><PERSON>", "main": "index.js", "scripts": {"dev": "nodemon index.js --agent local_connection_batch_100", "agent": "node tests/agent.js --agent local_connection_batch_100", "outbound_csv": "node index.js --agent local_connection_outbound_batch_100", "outbound_xml": "node index.js --agent ccure9000_xml_outbound_local", "test_api_1": "node tests/testApi1.js", "test_api_2": "node tests/testApi2.js", "outbound_api_1": "node index.js --agent api_1_outbound", "outbound_api_2": "node index.js --agent api_2_outbound", "db": "node scripts/dbSync.js", "db:refresh": "node scripts/dbSync.js refresh"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@azure/storage-blob": "^12.27.0", "axios": "^1.9.0", "basic-ftp": "^5.0.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-winston": "^4.2.0", "fast-csv": "^5.0.2", "install": "^0.13.0", "joi": "^17.13.3", "node-cron": "^4.0.6", "npm": "^11.4.2", "ssh2-sftp-client": "^12.0.0", "uuid": "^10.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}}