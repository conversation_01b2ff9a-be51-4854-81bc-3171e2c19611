const config = require("../config/config");
const logger = require("../config/logger");
const nodemailer = require("nodemailer");
const models = require("../models");
const ejs = require("ejs");
const { getCachedNotificationEmail } = require("../helpers/caching.helper");

// Create a reusable transporter object using SMTP transport
const transporter = nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: false, // upgrade later with STARTTLS
  auth: {
    user: config.email.user,
    pass: config.email.pass,
  },
});

/**
 * Send an email notification based on an event.
 *
 * @param {Object} eventInstance - The event payload.
 * @param {string} eventInstance.event_type - The type of event (e.g. "guestArrived").
 * @param {string|number} eventInstance.parent_id - FK to the related record.
 * @param {Object} eventInstance.notification - Notification metadata.
 * @param {string} eventInstance.notification.template - EJS template filename (without extension).
 * @param {string} eventInstance.notification.subject - Email subject line.
 * @param {Object} context - Execution context with tracing info.
 * @param {string} context.trace_id - Unique ID for correlating logs.
 * @returns {Promise<void>} Resolves when the email is sent or skips if data is missing.
 * @throws {Error} If a database lookup or the sendMail call fails.
 */
async function sendEmail(eventInstance, context) {
  const { event_type, notification, parent_id } = eventInstance
  const { trace_id } = context;
  logger.info(`[TRACE ${trace_id}] sendEmail invoked for event "${event_type}"`);

  try {
    const { notification_child_id, schema, schema_column } = notification;
    if (!notification_child_id && !schema && !schema_column) throw "Insufficient data supplied"

    const notificationEmail = await getCachedNotificationEmail(notification_child_id);
    if (!notificationEmail) throw `NotificationEmail "${notification.notification_child_id}" not found`;

    const { subject, receiver_column, template } = notificationEmail;
    const Model = models[schema];
    if (!Model) throw `Model "${modelName}" not found`;
    const templateData = await Model.findOne({ where: { [schema_column]: parent_id } })
    const content = ejs.render(template, templateData);
    const to = templateData[receiver_column];

    // Prepare email options
    const mailOptions = {
      from: config.email.user,
      to,
      subject,
      html:content,
    };

    // Send email
    await transporter.sendMail(mailOptions);
    logger.info(`[TRACE ${trace_id}] Email successfully sent to ${to}`);
  } catch (err) {
    logger.error(`[TRACE ${trace_id}] sendEmail error: ${err.message}`);
    throw err;
  }
}

module.exports = sendEmail;
