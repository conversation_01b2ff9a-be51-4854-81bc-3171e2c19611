#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Tyk Gateway + API Flow
 * Tests all endpoints through the Tyk gateway to verify authentication and authorization
 */

// Use built-in fetch for Node.js 18+ or fallback to https module
let fetch;
try {
    fetch = globalThis.fetch || require('node-fetch');
} catch (e) {
    // Fallback implementation using https module
    const https = require('https');
    const http = require('http');
    const { URL } = require('url');

    fetch = async (url, options = {}) => {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const isHttps = urlObj.protocol === 'https:';
            const client = isHttps ? https : http;

            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port || (isHttps ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: options.headers || {}
            };

            const req = client.request(requestOptions, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        ok: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        json: () => Promise.resolve(JSON.parse(data)),
                        text: () => Promise.resolve(data)
                    });
                });
            });

            req.on('error', reject);

            if (options.body) {
                req.write(options.body);
            }

            req.end();
        });
    };
}

// Configuration
const TYK_BASE_URL = 'http://localhost:8181';
const TEST_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'Pa$$w0rd!'
};

// Test results storage
const testResults = [];
let accessToken = null;

// Utility functions
function log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${type}: ${message}`);
}

function logResult(testName, success, response = null, error = null) {
    const result = {
        test: testName,
        success,
        timestamp: new Date().toISOString(),
        response: response ? {
            status: response.status,
            statusText: response.statusText,
            data: response.data
        } : null,
        error: error ? error.message : null
    };
    
    testResults.push(result);
    
    const status = success ? '✅ PASS' : '❌ FAIL';
    log(`${status} - ${testName}`, success ? 'PASS' : 'FAIL');
    
    if (error) {
        log(`Error: ${error.message}`, 'ERROR');
    }
}

async function makeRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        
        let data;
        try {
            data = await response.json();
        } catch (e) {
            data = await response.text();
        }
        
        return {
            success: response.ok,
            status: response.status,
            statusText: response.statusText,
            data
        };
    } catch (error) {
        return {
            success: false,
            error
        };
    }
}

// Test 1: Gateway Health Check
async function testGatewayHealth() {
    log('Testing Tyk Gateway health check...');
    
    const result = await makeRequest(`${TYK_BASE_URL}/hello`);
    
    const success = result.success && 
                   result.data && 
                   result.data.status === 'pass' && 
                   result.data.version;
    
    logResult('Gateway Health Check', success, result, result.error);
    return success;
}

// Test 2: Public API Health (No Auth)
async function testPublicApiHealth() {
    log('Testing public API health endpoint (no auth required)...');
    
    const result = await makeRequest(`${TYK_BASE_URL}/caremate/api/health`);
    
    const success = result.success && 
                   result.data && 
                   result.data.status === true &&
                   result.data.message === 'Service is healthy';
    
    logResult('Public API Health', success, result, result.error);
    return success;
}

// Test 3: Authentication (Login)
async function testAuthentication() {
    log('Testing authentication (login)...');
    
    const result = await makeRequest(`${TYK_BASE_URL}/caremate/api/auth/login`, {
        method: 'POST',
        body: JSON.stringify(TEST_CREDENTIALS)
    });
    
    const success = result.success && 
                   result.data && 
                   result.data.status === true &&
                   result.data.data &&
                   result.data.data.tokens &&
                   result.data.data.tokens.access &&
                   result.data.data.tokens.access.token;
    
    if (success) {
        accessToken = result.data.data.tokens.access.token;
        log(`Access token obtained: ${accessToken.substring(0, 50)}...`);
    }
    
    logResult('Authentication (Login)', success, result, result.error);
    return success;
}

// Test 4: Authenticated Endpoint (No Authorization Required)
async function testAuthenticatedEndpoint() {
    log('Testing authenticated endpoint (profile/language)...');
    
    if (!accessToken) {
        const error = new Error('No access token available');
        logResult('Authenticated Endpoint', false, null, error);
        return false;
    }
    
    const result = await makeRequest(`${TYK_BASE_URL}/caremate/protected/profile/language`, {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({
            language_id: '701d452e-07a3-4f4c-8858-eb6bfdcbbda9'
        })
    });
    
    const success = result.success && 
                   result.data && 
                   result.data.status === true &&
                   result.data.message === 'Language preference updated successfully.';
    
    logResult('Authenticated Endpoint (Profile/Language)', success, result, result.error);
    return success;
}

// Test 5: Authenticated + Authorized Endpoint
async function testAuthorizedEndpoint() {
    log('Testing authenticated + authorized endpoint (facility)...');
    
    if (!accessToken) {
        const error = new Error('No access token available');
        logResult('Authorized Endpoint', false, null, error);
        return false;
    }
    
    const result = await makeRequest(`${TYK_BASE_URL}/caremate/protected/facility`, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${accessToken}`
        }
    });
    
    const success = result.success && 
                   result.data && 
                   result.data.status === true &&
                   result.data.data &&
                   Array.isArray(result.data.data.data) &&
                   result.data.message === 'Facilities retrieved successfully';
    
    logResult('Authorized Endpoint (Facility)', success, result, result.error);
    return success;
}

// Test 6: Unauthorized Access Test
async function testUnauthorizedAccess() {
    log('Testing unauthorized access (should fail)...');
    
    const result = await makeRequest(`${TYK_BASE_URL}/caremate/protected/facility`, {
        method: 'GET'
        // No Authorization header
    });
    
    // This should fail with 401 or 403
    const success = !result.success && (result.status === 401 || result.status === 403);
    
    logResult('Unauthorized Access Test', success, result, result.error);
    return success;
}

// Main test runner
async function runAllTests() {
    log('Starting Tyk Gateway + API Flow Tests');
    log('==========================================');
    
    const tests = [
        { name: 'Gateway Health', fn: testGatewayHealth },
        { name: 'Public API Health', fn: testPublicApiHealth },
        { name: 'Authentication', fn: testAuthentication },
        { name: 'Authenticated Endpoint', fn: testAuthenticatedEndpoint },
        { name: 'Authorized Endpoint', fn: testAuthorizedEndpoint },
        { name: 'Unauthorized Access', fn: testUnauthorizedAccess }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        try {
            const result = await test.fn();
            if (result) passedTests++;
        } catch (error) {
            logResult(test.name, false, null, error);
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    log('==========================================');
    log(`Test Summary: ${passedTests}/${tests.length} tests passed`);
    
    // Print detailed results
    console.log('\nDetailed Results:');
    console.log(JSON.stringify(testResults, null, 2));
    
    return passedTests === tests.length;
}

// Run tests if this file is executed directly
if (require.main === module) {
    runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            log(`Fatal error: ${error.message}`, 'ERROR');
            process.exit(1);
        });
}

module.exports = {
    runAllTests,
    testResults
};
