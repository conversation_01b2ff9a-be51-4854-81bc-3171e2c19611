const express = require('express');
const validate = require('../middlewares/validate');
const { PatientValidation,PatientGuestValidation } = require('../validations');
const { <PERSON><PERSON><PERSON>ontroller ,PatientGuestController } = require('../controllers');
const auth = require('../middlewares/auth');
const hipaaLogger = require('../middlewares/hipaaLogger');
const uploadToBase64 = require('../middlewares/upload');

const router = express.Router();

/**
 * @swagger
 * /patients:
 *   get:
 *     summary: Get all patient appointments (paginated)
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by patient first name or last name
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by appointment status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort appointments by (e.g., "appointment_date", "first_name", "last_name"). Default is "appointment_date".
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Order of sorting for appointments (ASC for ascending, DESC for descending). Default is "ASC".
 *     responses:
 *       200:
 *         description: Paginated list of patient appointments.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 50
 *               totalPages: 5
 *               currentPage: 1
 *               data:
 *                 - appointment_id: "64b8f0e2d123e4567890abcd"
 *                   patient_id: "123e4567-e89b-12d3-a456-************"
 *                   appointment_date: "2025-04-18T10:00:00.000Z"
 *                   department: "Cardiology"
 *                   provider_name: "Dr. John Doe"
 *                   appointment_status: 1
 *                   appointment_status_name: "Scheduled"
 *                   type: 2
 *                   appointment_type_name: "Follow-up"
 *                   facility_id: "123e4567-e89b-12d3-a456-************"
 *                   room_number: "101"
 *                   floor_number: "2"
 *                   beds: "2"
 *                   first_name: "Jane"
 *                   last_name: "Doe"
 *                   birth_date: "1990-01-01T00:00:00.000Z"
 *                   patient_gender: 1
 *                   mrn: "MRN12345"
 *                   facility_name: "Central Hospital"
 */
router.get(
  '/',
  auth('view_patients'),
  validate(PatientValidation.getPatientAppointments),
  hipaaLogger,
  PatientController.view
);

/**
 * @swagger
 * /patients/details:
 *   get:
 *     summary: Retrieve appointment details for a specific patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose appointment details are being retrieved.
 *     responses:
 *       200:
 *         description: Appointment details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient appointment details retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "John"
 *                       last_name:
 *                         type: string
 *                         example: "Doe"
 *                       mrn:
 *                         type: string
 *                         example: "123456789"
 *                       appointment_type_name:
 *                         type: string
 *                         example: "Regular Checkup"
 *                       confidentiality_code:
 *                         type: integer
 *                         example: 1
 *                       facility_name:
 *                         type: string
 *                         example: "Main Hospital"
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 */
router.get(
  '/details',
  auth('view_patients'),
  validate(PatientValidation.getPatientDetails),
  hipaaLogger,
  PatientController.single
);
/**
 * @swagger
 * /patients/information:
 *   get:
 *     summary: Retrieve the latest appointment details for a specific patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose appointment details are being retrieved.
 *     responses:
 *       200:
 *         description: Latest appointment details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Latest patient appointment details retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     first_name:
 *                       type: string
 *                     last_name:
 *                       type: string
 *                     appointment_date:
 *                       type: string
 *                       format: date-time
 *                     facility_name:
 *                       type: string
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 *       404:
 *         description: No appointment found for the given patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No appointment found for the given patient
 */
router.get(
  '/information',
  auth('view_patients'),
  validate(PatientValidation.getPatientInformation),
  hipaaLogger,
  PatientController.information
);

/**
 * @swagger
 * /patients/{patient_id}/image:
 *   patch:
 *     summary: Update the image for a patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose image is being updated.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Patient image updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient image updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     patient_id:
 *                       type: string
 *                       format: uuid
 *                     image:
 *                       type: string
 *                       format: uri
 *                       example: "https://example.com/new-image.jpg"
 *       400:
 *         description: Bad request if image data is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Image data is required
 *       404:
 *         description: Patient not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient not found
 */
router.patch(
  '/:patient_id/image',
  auth('update_patient'),
  uploadToBase64("image"),
  validate(PatientValidation.updateImage), // Add validation
  PatientController.image
);

/**
 * @swagger
 * /patients/{patient_id}/details:
 *   patch:
 *     summary: Update patient details
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose details are being updated.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               middle_name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               preferred_name:
 *                 type: string
 *               birth_date:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Patient details updated successfully.
 */
router.patch(
  "/:patient_id/details",
  auth("update_patient"),
  validate(PatientValidation.updatePatientDetails), // Add validation
  PatientController.updatePatientDetails
);

/**
 * @swagger
 * /patients/{patient_id}/admission:
 *   patch:
 *     summary: Update admission details
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose admission details are being updated.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: integer
 *               arrival_time:
 *                 type: string
 *                 format: date-time
 *               discharge_time:
 *                 type: string
 *                 format: date-time
 *               death_date:
 *                 type: string
 *                 format: date
 *               confidentiality_code:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Admission details updated successfully.
 */
router.patch(
  "/:patient_id/admission",
  auth("update_patient"),
  validate(PatientValidation.updateAdmissionDetails), // Add validation
  PatientController.updateAdmissionDetails
);

/**
 * @swagger
 * /patients/{patient_id}/facility:
 *   patch:
 *     summary: Update facility details
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose facility details are being updated.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               facility_id:
 *                 type: string
 *                 description: The ID of the facility to update.
 *               facility_name:
 *                 type: string
 *                 description: The new name for the facility.
 *               building_id:
 *                 type: string
 *                 description: The ID of the building to update.
 *               building_name:
 *                 type: string
 *                 description: The new name for the building.
 *               floor_id:
 *                 type: string
 *                 description: The ID of the floor to update.
 *               floor_number:
 *                 type: integer
 *                 description: The new floor number.
 *               room_id:
 *                 type: string
 *                 description: The ID of the room to update.
 *               room_number:
 *                 type: string
 *                 description: The new room number.
 *               beds:
 *                 type: integer
 *                 description: The number of beds available in the room.
 *     responses:
 *       200:
 *         description: Facility details updated successfully.
 *       404:
 *         description: Facility, building, floor, or appointment not found.
 */
router.patch(
  "/:patient_id/facility",
  auth("update_patient"),
  validate(PatientValidation.updateFacilityDetails), // Add validation
  PatientController.updateFacilityDetails
);
/**
 * @swagger
 * /patients/{patient_id}/address:
 *   patch:
 *     summary: Update or create patient address
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose address is being updated or created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               address_line_1:
 *                 type: string
 *                 example: "123 Main St"
 *               address_line_2:
 *                 type: string
 *                 example: "Apt 4B"
 *               country:
 *                 type: string
 *                 example: "USA"
 *               state:
 *                 type: string
 *                 example: "California"
 *               city:
 *                 type: string
 *                 example: "Los Angeles"
 *               postal_code:
 *                 type: string
 *                 example: "90001"
 *     responses:
 *       200:
 *         description: Address details updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Address details updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     address_line_1:
 *                       type: string
 *                     address_line_2:
 *                       type: string
 *                     country:
 *                       type: string
 *                     state:
 *                       type: string
 *                     city:
 *                       type: string
 *                     postal_code:
 *                       type: string
 *       201:
 *         description: Address created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Address created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     address_line_1:
 *                       type: string
 *                     address_line_2:
 *                       type: string
 *                     country:
 *                       type: string
 *                     state:
 *                       type: string
 *                     city:
 *                       type: string
 *                     postal_code:
 *                       type: string
 *       404:
 *         description: Patient not found.
 */
router.patch(
  "/:patient_id/address",
  auth("update_patient"),
  validate(PatientValidation.updateAddress), // Add validation
  PatientController.updateAddress
);


/**
 * @swagger
 * /patients/guests/list:
 *   get:
 *     summary: Retrieve guests for a specific patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose guests are being retrieved.
 *       - in: query
 *         name: search
 *         required: false
 *         schema:
 *           type: string
 *         description: Optional search term to filter guests by name.
 *       - in: query
 *         name: appointment_status
 *         required: false
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2]
 *         description: Filter guests by appointment status (0, 1, or 2).
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort guests by (e.g., "guest_arrival_time","escort_name"). Default is "guest_arrival_time".
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Order of sorting for guests (ASC for ascending, DESC for descending). Default is "DESC".
 *     responses:
 *       200:
 *         description: A list of guests for the specified patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guests retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "John"
 *                       last_name:
 *                         type: string
 *                         example: "John"
 *                       guest_arrival_time:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-04-18T10:00:00Z"
 *                       guest_departure_time:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-04-18T11:00:00Z"
 *                       facility_name:
 *                         type: string
 *                         example: "Main Hospital"
 *                       building_name:
 *                         type: string
 *                         example: "Building A"
 *                       floor_number:
 *                         type: integer
 *                         example: 2
 *                       room_number:
 *                         type: string
 *                         example: "201"
 *                       appointment_status:
 *                         type: integer
 *                         example: 1
 *                       appointment_status_name:
 *                         type: string
 *                         example: "Checked In"
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 *       404:
 *         description: No guests found for the specified patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No guests found for the specified patient.
 */
router.get(
  "/guests/list",
  auth("view_guests"),
  validate(PatientGuestValidation.guestList),
  hipaaLogger,
  PatientGuestController.guestList
);

/**
 * @swagger
 * /patients/guests/{patient_guest_id}:
 *   patch:
 *     summary: Update a guest's details
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient guest to be updated.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: Frensch
 *               last_name:
 *                 type: string
 *                 example: Doe
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               phone:
 *                 type: string
 *                 example: "+**********"
 *               guest_type:
 *                 type: integer
 *                 example: 1
 *               relationship_type:
 *                 type: integer
 *                 example: 1
 *               reason:
 *                type: string
 *                example: "Reason for update"
 *               denied_on:
 *                type: string
 *                format: date-time
 *     responses:
 *       200:
 *         description: Guest updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guest updated successfully
 *                 data:
 *                   $ref: '#/components/schemas/GuestResponse'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 */
router.patch(
  "/guests/:patient_guest_id",
  auth("update_guest"),
  validate(PatientGuestValidation.update),
  PatientGuestController.updateGuest
);

/**
 * @swagger
 * /patients/guests/{patient_guest_id}:
 *   delete:
 *     summary: Delete a guest by ID
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/GuestIdPath'
 *     responses:
 *       204:
 *         description: Guest deleted successfully
 *       404:
 *         $ref: '#/components/responses/NotFound'
 */
router.delete(
  "/guests/:patient_guest_id",
  auth("delete_guest"),
  validate(PatientGuestValidation.deleteGuest), // Add validation
  PatientGuestController.deleteGuest
);

/**
 * @swagger
 * /patients/guests/friends:
 *   get:
 *     summary: Retrieve friends of a specific patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose friends are being retrieved.
 *       - in: query
 *         name: search
 *         required: false
 *         schema:
 *           type: string
 *         description: Optional search term to filter friends by name.
 *       - in: query
 *         name: guest_type
 *         required: false
 *         schema:
 *           type: integer
 *         description: Filter by guest type (1 for friends).
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort guests by (e.g., "first_name","last_name"). Default is "first_name".
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Order of sorting for guests (ASC for ascending, DESC for descending). Default is "ASC".
 *     responses:
 *       200:
 *         description: A list of friends for the specified patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guests retrieved successfully for the patient
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "John"
 *                       last_name:
 *                         type: string
 *                         example: "Doe"
 *                       email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       phone:
 *                         type: string
 *                         example: "+**********"
 *                       mrn:
 *                         type: string
 *                         example: "MRN12345"
 *                       relationship_type:
 *                         type: integer
 *                         example: 1
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 */
router.get(
  "/guests/friends",
  auth("view_guests"),
  validate(PatientGuestValidation.friends), // Add validation
  hipaaLogger,
  PatientGuestController.friends
);

/**
 * @swagger
 * /patients/guests/denied:
 *   get:
 *     summary: Retrieve denied guests for a specific patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose denied guests are being retrieved.
 *       - in: query
 *         name: search
 *         required: false
 *         schema:
 *           type: string
 *         description: Optional search term to filter denied guests by name.
 *       - in: query
 *         name: guest_type
 *         required: false
 *         schema:
 *           type: integer
 *         description: Filter by guest type (2 for denied guests).
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort guests by (e.g., "first_name","last_name"). Default is "first_name".
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Order of sorting for guests (ASC for ascending, DESC for descending). Default is "ASC".
 *     responses:
 *       200:
 *         description: A list of denied guests for the specified patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Guests retrieved successfully for the patient
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "Jane Doe"
 *                       last_name:
 *                         type: string
 *                         example: "Jane Doe"
 *                       email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       phone:
 *                         type: string
 *                         example: "+**********"
 *                       birth_date:
 *                         type: string
 *                         format: date
 *                         example: "1990-01-01"
 *                       reason:
 *                         type: string
 *                         example: "Security concerns"
 *                       denied_on:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-04-18T10:00:00Z"
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 */
router.get(
  "/guests/denied",
  auth("view_guests"),
  validate(PatientValidation.denied),
  hipaaLogger,
  PatientGuestController.denied
);
/**
 * @swagger
 * /patients/guests/add:
 *   post:
 *     summary: Add a Denied Guest or Friend
 *     description: This endpoint adds a denied guest or friend to the system.
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               guest_type:
 *                 type: integer
 *               birth_date:
 *                 type: string
 *                 format: date
 *                 example: "1990-01-01"
 *               reason:
 *                 type: string
 *               denied_on:
 *                 type: string
 *                 format: date-time
 *               patient_id:
 *                 type: string
 *               relationship_type:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Denied Guest added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                 patientGuest:
 *                   type: object
 *       400:
 *         description: Reason is required when guest_type is 2
 *       404:
 *         description: Guest not found
 */
router.post('/guests/add',   auth("create_guest"),validate(PatientGuestValidation.denied),  PatientGuestController.addGuest);

/**
 * @swagger
 * /patients/history:
 *   get:
 *     summary: Retrieve patient history
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the patient whose history is being retrieved.
 *     responses:
 *       200:
 *         description: Patient history retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient history retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       effective_date:
 *                         type: string
 *                         format: date-time
 *                       patient_history_id:
 *                         type: string
 *                       field_changes:
 *                         type: string
 *                       old_value:
 *                         type: string
 *                       new_value:
 *                         type: string
 *                       event_type:
 *                         type: string
 *                       patient_id:
 *                         type: string
 *                       modified_by:
 *                         type: string
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 *       404:
 *         description: No history found for the given patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No history found for the given patient
 */
router.get(
  '/history',
  auth('view_patients'),
  validate(PatientValidation.getPatientHistory),
  hipaaLogger,
  PatientController.getPatientHistory
);
/**
 * @swagger
 * /patients/history/allpatient:
 *   get:
 *     summary: Retrieve patient history
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Patient history retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient history retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       effective_date:
 *                         type: string
 *                         format: date-time
 *                       patient_history_id:
 *                         type: string
 *                       field_changes:
 *                         type: string
 *                       old_value:
 *                         type: string
 *                       new_value:
 *                         type: string
 *                       event_type:
 *                         type: string
 *                       patient_id:
 *                         type: string
 *                       modified_by:
 *                         type: string
 *       400:
 *         description: Bad request if patient_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: patient_id is required
 *       404:
 *         description: No history found for the given patient.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No history found for the given patient
 */
router.get(
  '/history/allpatient',
  auth('view_patients'),
  hipaaLogger,
  validate(PatientController.getAllPatientHistory),
  PatientController.getAllPatientHistory
);

/**
 * @swagger
 * /patients/search:
 *   get:
 *     summary: Search patients with optional appointment type filtering
 *     description: Search patients by first name, last name, MRN, or preferred name. Optionally filter by appointment type.
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to match against first name, last name, MRN, or preferred name.
 *         example: "John"
 *       - in: query
 *         name: type
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: Optional appointment type filter (0 for inpatient, 1 for outpatient). When provided, only returns patients with appointments of this type.
 *         example: 0
 *     responses:
 *       200:
 *         description: Patients retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Patients retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       first_name:
 *                         type: string
 *                         example: "John"
 *                       last_name:
 *                         type: string
 *                         example: "Doe"
 *                       image:
 *                         type: string
 *                         description: Base64 encoded image or image URL
 *                       mrn:
 *                         type: string
 *                         example: "MRN12345"
 *                       patient_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                       preferred_name:
 *                         type: string
 *                         example: "Johnny"
 *                       birth_date:
 *                         type: string
 *                         format: date
 *                         example: "1990-01-15"
 *                       appointment_type:
 *                         type: integer
 *                         description: Only present when filtering by type
 *                         example: 0
 *                       appointment_id:
 *                         type: string
 *                         format: uuid
 *                         description: Only present when filtering by type
 *                         example: "456e7890-e89b-12d3-a456-************"
 *                       appointment_date:
 *                         type: string
 *                         format: date
 *                         description: Only present when filtering by type
 *                         example: "2024-01-20"
 *       400:
 *         description: Invalid query parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  "/search",
  auth("view_patients"),
  validate(PatientValidation.searchPatients),
  PatientController.search
);

/**
 * @swagger
 * /patients/hl7-messages/{mrn}:
 *   get:
 *     summary: Get paginated HL7 messages for a patient by MRN, with dynamic search and sorting
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: mrn
 *         required: true
 *         schema:
 *           type: string
 *         description: Patient MRN
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of records per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Case‑insensitive search term applied across all message fields
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: processed_at
 *         description: Field name to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Paginated list of HL7 messages.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 50
 *               totalPages: 5
 *               currentPage: 1
 *               data:
 *                 - hl7_message_id: "64b8f0e2d123e4567890abcd"
 *                   mrn: "MRN12345"
 *                   message_type: "ADT"
 *                   message: "Sample HL7 message content"
 *                   processed_at: "2025-05-09T10:00:00.000Z"
 */

router.get(
  '/hl7-messages/:mrn',
  auth('view_hl7_messages'),
  validate(PatientValidation.getHl7Messages),
  PatientController.getHl7Messages
);
/**
 * @swagger
 * /patients/guests/{patient_guest_id}/friend:
 *   put:
 *     summary: Update a friend (guest_type = 1) in PatientGuest
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the friend (PatientGuest) to update.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               relationship_type:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Friend updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Friend updated successfully
 *                 data:
 *                   type: object
 *       400:
 *         description: Not a friend record or bad request.
 *       404:
 *         description: Friend not found.
 */
router.put(
  "/guests/:patient_guest_id/friend",
  auth("update_guest"),
  validate(PatientGuestValidation.updateFriend),
  PatientGuestController.updateFriend
);


module.exports = router;
