"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const applicationTypes = [
      {
        application_type_id: uuidv4(),
        name: "api",
        display_name: "API",
        description: "API service",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        application_type_id: uuidv4(),
        name: "processor",
        display_name: "Event Processor",
        description: "Event processor service",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        application_type_id: uuidv4(),
        name: "agent",
        display_name: "Agent",
        description: "Agent service",
        created_at: new Date(),
        updated_at: new Date(),
      }
    ];

    await queryInterface.bulkInsert("application_type", applicationTypes, {});
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("application_type", null, {});
  },
};
