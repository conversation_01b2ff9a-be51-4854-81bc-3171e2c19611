
const express = require('express');
const validate = require('../middlewares/validate');
const visitorHubValidation = require('../validations/visitorHub.validation');
const visitorHubController = require('../controllers/visitorHub.controller');

const router = express.Router();

/**
 * @swagger
 * /visitor-hub/hosts/search:
 *   get:
 *     summary: Search Host by Name and Facility
 *     tags:
 *       - VisitorHub
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Partial or full name of the host
 *       - in: query
 *         name: facility_id
 *         schema:
 *           type: string
 *           format: uuid
 *         required: true
 *         description: Facility ID to filter
 *     responses:
 *       200:
 *         description: List of matching hosts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '../docs/components.yml#/components/schemas/HostDetails'
 *       400:
 *         description: Validation error
 *       500:
 *         description: Server error
 */
router.get(
  '/hosts/search',
  validate(visitorHubValidation.searchHost.query, 'query'),
  visitorHubController.searchHost
);

module.exports = router;
