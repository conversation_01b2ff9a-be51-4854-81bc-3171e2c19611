const logger = require('../config/logger');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');

/**
 * Performance monitoring utility for tracking time, memory usage, and efficiency metrics
 */
class PerformanceMonitor {
  constructor(processName = 'Unknown Process') {
    this.processName = processName;
    this.startTime = process.hrtime.bigint();
    this.startMemory = process.memoryUsage();
    this.sessionId = this.generateSessionId();
    this.performanceDir = path.join(process.cwd(), 'performances');
    this.performanceFile = path.join(this.performanceDir, `${this.sessionId}.log`);

    this.metrics = {
      sessionId: this.sessionId,
      processName,
      startTime: new Date().toISOString(),
      steps: [],
      summary: {},
      customMetrics: [],
      errors: [],
      validationErrors: [],
      apiErrors: []
    };
    this.stepTimers = new Map();

    // Batched logging for performance
    this.logBuffer = [];
    this.bufferSize = 50; // Write to file every 50 entries
    this.lastFlush = Date.now();
    this.flushInterval = 5000; // Force flush every 5 seconds

    // Ensure performances directory exists
    this.ensurePerformanceDirectory();

    // Initialize performance file
    this.initializePerformanceFile();

    // Set up periodic buffer flush
    this.flushTimer = setInterval(() => {
      this.flushLogBuffer();
    }, this.flushInterval);

    logger.info(`[Performance] Starting performance monitoring for: ${processName}`, {
      sessionId: this.sessionId,
      performanceFile: this.performanceFile,
      initialMemory: this.formatMemoryUsage(this.startMemory)
    });
  }

  /**
   * Generate a unique session ID for this performance monitoring instance
   */
  generateSessionId() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const processName = (this.processName && typeof this.processName === 'string') ?
      this.processName.replace(/[^a-zA-Z0-9]/g, '_') : 'unknown';
    return `${timestamp}_${processName}`;
  }

  /**
   * Ensure the performances directory exists
   */
  ensurePerformanceDirectory() {
    try {
      if (!fsSync.existsSync(this.performanceDir)) {
        fsSync.mkdirSync(this.performanceDir, { recursive: true });
        logger.info(`[Performance] Created performances directory: ${this.performanceDir}`);
      }
    } catch (error) {
      logger.error(`[Performance] Failed to create performances directory: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add log entry to buffer for batched writing
   */
  addToLogBuffer(logEntry) {
    this.logBuffer.push(logEntry);

    // Flush if buffer is full
    if (this.logBuffer.length >= this.bufferSize) {
      this.flushLogBuffer();
    }
  }

  /**
   * Flush log buffer to file asynchronously
   */
  async flushLogBuffer() {
    if (this.logBuffer.length === 0) return;

    const entriesToWrite = [...this.logBuffer];
    this.logBuffer = [];
    this.lastFlush = Date.now();

    try {
      const content = entriesToWrite.join('');
      await fs.appendFile(this.performanceFile, content);
    } catch (error) {
      logger.error(`[Performance] Failed to write to performance file: ${error.message}`);
      // Put entries back in buffer if write failed
      this.logBuffer.unshift(...entriesToWrite);
    }
  }

  /**
   * Initialize the performance file with basic metadata
   */
  initializePerformanceFile() {
    try {
      const header = this.generateLogHeader();
      fsSync.writeFileSync(this.performanceFile, header);
      logger.info(`[Performance] Initialized performance file: ${this.performanceFile}`);
    } catch (error) {
      logger.error(`[Performance] Failed to initialize performance file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate log file header
   */
  generateLogHeader() {
    const startMemory = this.formatMemoryUsage(this.startMemory);
    return `
================================================================================
                        PERFORMANCE MONITORING LOG
================================================================================
Session ID: ${this.sessionId}
Process Name: ${this.processName}
Start Time: ${this.metrics.startTime}
Initial Memory Usage:
  - RSS: ${startMemory.rss}
  - Heap Total: ${startMemory.heapTotal}
  - Heap Used: ${startMemory.heapUsed}
  - External: ${startMemory.external}

================================================================================
                              EXECUTION STEPS
================================================================================

`;
  }

  /**
   * Append text to the performance log file (using buffer for performance)
   */
  appendToFile(text) {
    try {
      // Use buffered logging for better performance
      this.addToLogBuffer(text);
    } catch (error) {
      logger.error(`[Performance] Failed to add to log buffer: ${error.message}`);
      // Don't throw here to avoid breaking the main process
    }
  }

  /**
   * Start timing a specific step
   * @param {string} stepName - Name of the step to track
   * @param {Object} metadata - Additional metadata for the step
   */
  startStep(stepName, metadata = {}) {
    const stepStartTime = process.hrtime.bigint();
    const stepStartMemory = process.memoryUsage();

    this.stepTimers.set(stepName, {
      startTime: stepStartTime,
      startMemory: stepStartMemory,
      metadata
    });

    // Write step start to file
    const startLog = `[${new Date().toISOString()}] STARTED: ${stepName}\n`;
    this.appendToFile(startLog);

    logger.info(`[Performance] Starting step: ${stepName}`, {
      sessionId: this.sessionId,
      stepMetadata: metadata
    });
  }

  /**
   * End timing a specific step and record metrics
   * @param {string} stepName - Name of the step to end
   * @param {Object} results - Results/metrics from the step
   */
  endStep(stepName, results = {}) {
    const stepTimer = this.stepTimers.get(stepName);
    if (!stepTimer) {
      logger.warn(`[Performance] No timer found for step: ${stepName}`);
      return;
    }

    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    const duration = Number(endTime - stepTimer.startTime) / 1e6; // Convert to milliseconds

    const stepMetrics = {
      stepName,
      startTime: new Date(Date.now() - duration).toISOString(),
      endTime: new Date().toISOString(),
      durationMs: Math.round(duration * 100) / 100,
      durationFormatted: this.formatDuration(duration),
      memoryBefore: stepTimer.startMemory,
      memoryAfter: endMemory,
      memoryDelta: this.calculateMemoryDelta(stepTimer.startMemory, endMemory),
      metadata: stepTimer.metadata,
      results
    };

    this.metrics.steps.push(stepMetrics);
    this.stepTimers.delete(stepName);

    // Write step completion to file
    this.writeStepCompletion(stepMetrics);

    logger.info(`[Performance] Completed step: ${stepName}`, {
      sessionId: this.sessionId,
      duration: stepMetrics.durationFormatted
    });

    return stepMetrics;
  }

  /**
   * Write step completion details to log file
   */
  writeStepCompletion(stepMetrics) {
    const { stepName, durationFormatted, memoryDelta, results, metadata } = stepMetrics;
    const memoryChange = memoryDelta.rssChange > 0 ? `+${memoryDelta.rssChange}` : `${memoryDelta.rssChange}`;

    let logEntry = `[${new Date().toISOString()}] COMPLETED: ${stepName}\n`;
    logEntry += `  ⏱️  Duration: ${durationFormatted}\n`;
    logEntry += `  💾 Memory Change: ${memoryChange} MB (RSS)\n`;

    // Add metadata if present
    if (metadata && Object.keys(metadata).length > 0) {
      logEntry += `  📋 Metadata:\n`;
      Object.entries(metadata).forEach(([key, value]) => {
        logEntry += `     - ${key}: ${value}\n`;
      });
    }

    // Add results/metrics if present
    if (results && Object.keys(results).length > 0) {
      logEntry += `  📊 Results:\n`;
      Object.entries(results).forEach(([key, value]) => {
        logEntry += `     - ${key}: ${value}\n`;
      });
    }

    logEntry += `\n`;
    this.appendToFile(logEntry);
  }

  /**
   * Write progress update to log file
   */
  writeProgressUpdate(message, data) {
    const currentTime = process.hrtime.bigint();
    const elapsedTime = Number(currentTime - this.startTime) / 1e6;
    const currentMemory = process.memoryUsage();
    const memoryFormatted = this.formatMemoryUsage(currentMemory);

    let logEntry = `[${new Date().toISOString()}] PROGRESS: ${message}\n`;
    logEntry += `  ⏱️  Elapsed Time: ${this.formatDuration(elapsedTime)}\n`;
    logEntry += `  💾 Current Memory: ${memoryFormatted.rss} RSS\n`;

    if (data && Object.keys(data).length > 0) {
      logEntry += `  📊 Data:\n`;
      Object.entries(data).forEach(([key, value]) => {
        logEntry += `     - ${key}: ${value}\n`;
      });
    }

    logEntry += `\n`;
    this.appendToFile(logEntry);
  }

  /**
   * Add a metric without timing (for counters, etc.)
   * @param {string} metricName - Name of the metric
   * @param {*} value - Value of the metric
   * @param {Object} metadata - Additional metadata
   */
  addMetric(metricName, value, metadata = {}) {
    const metric = {
      metricName,
      value,
      timestamp: new Date().toISOString(),
      metadata
    };

    if (!this.metrics.customMetrics) {
      this.metrics.customMetrics = [];
    }
    this.metrics.customMetrics.push(metric);

    // Write metric to file
    let logEntry = `[${new Date().toISOString()}] METRIC: ${metricName}\n`;
    logEntry += `  📊 Value: ${value}\n`;

    if (metadata && Object.keys(metadata).length > 0) {
      logEntry += `  📋 Metadata:\n`;
      Object.entries(metadata).forEach(([key, val]) => {
        logEntry += `     - ${key}: ${val}\n`;
      });
    }

    logEntry += `\n`;
    this.appendToFile(logEntry);

    logger.info(`[Performance] Metric recorded: ${metricName}`, {
      sessionId: this.sessionId,
      value
    });
  }

  /**
   * Complete the performance monitoring and generate summary
   * @param {Object} finalResults - Final results of the process
   */
  async complete(finalResults = {}) {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    const totalDuration = Number(endTime - this.startTime) / 1e6; // Convert to milliseconds

    this.metrics.endTime = new Date().toISOString();
    this.metrics.totalDurationMs = Math.round(totalDuration * 100) / 100;
    this.metrics.totalDurationFormatted = this.formatDuration(totalDuration);
    this.metrics.finalMemory = endMemory;
    this.metrics.finalMemoryFormatted = this.formatMemoryUsage(endMemory);
    this.metrics.totalMemoryDelta = this.calculateMemoryDelta(this.startMemory, endMemory);
    this.metrics.finalResults = finalResults;
    this.metrics.status = finalResults.status || 'completed';

    // Generate summary statistics
    this.metrics.summary = this.generateSummary();

    // Write final completion to file
    this.writeFinalSummary();

    // Flush any remaining buffered logs
    await this.flushLogBuffer();

    // Clean up timer
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    logger.info(`[Performance] Process completed: ${this.processName}`, {
      sessionId: this.sessionId,
      totalDuration: this.metrics.totalDurationFormatted,
      performanceFile: this.performanceFile
    });

    return this.metrics;
  }

  /**
   * Write final summary to the performance log file
   */
  writeFinalSummary() {
    try {
      const finalMemory = this.formatMemoryUsage(this.metrics.finalMemory);
      const memoryChange = this.metrics.totalMemoryDelta.rssChange > 0 ?
        `+${this.metrics.totalMemoryDelta.rssChange}` :
        `${this.metrics.totalMemoryDelta.rssChange}`;

      let summary = `\n`;
      summary += `================================================================================\n`;
      summary += `                           PERFORMANCE SUMMARY\n`;
      summary += `================================================================================\n`;
      summary += `Process: ${this.processName}\n`;
      summary += `Status: ${this.metrics.status.toUpperCase()}\n`;
      summary += `End Time: ${this.metrics.endTime}\n`;
      summary += `Total Duration: ${this.metrics.totalDurationFormatted}\n`;
      summary += `Steps Completed: ${this.metrics.steps.length}\n`;
      summary += `\n`;
      summary += `Memory Usage:\n`;
      summary += `  - Final RSS: ${finalMemory.rss}\n`;
      summary += `  - Final Heap Used: ${finalMemory.heapUsed}\n`;
      summary += `  - Total Memory Change: ${memoryChange} MB (RSS)\n`;
      summary += `\n`;

      if (this.metrics.summary.stepBreakdown && this.metrics.summary.stepBreakdown.length > 0) {
        summary += `Step Performance Breakdown:\n`;
        this.metrics.summary.stepBreakdown.forEach(step => {
          summary += `  - ${step.name}: ${this.formatDuration(step.duration)} (${step.percentage}%)\n`;
        });
        summary += `\n`;
      }

      if (this.metrics.summary.longestStep) {
        summary += `Longest Step: ${this.metrics.summary.longestStep.name} (${this.formatDuration(this.metrics.summary.longestStep.duration)})\n`;
      }

      if (this.metrics.summary.shortestStep) {
        summary += `Shortest Step: ${this.metrics.summary.shortestStep.name} (${this.formatDuration(this.metrics.summary.shortestStep.duration)})\n`;
      }

      summary += `Average Step Duration: ${this.formatDuration(this.metrics.summary.averageStepDuration)}\n`;
      summary += `\n`;

      if (this.metrics.finalResults && Object.keys(this.metrics.finalResults).length > 0) {
        summary += `Final Results:\n`;
        Object.entries(this.metrics.finalResults).forEach(([key, value]) => {
          summary += `  - ${key}: ${value}\n`;
        });
        summary += `\n`;
      }

      // Add error summary
      const errorSummary = this.getErrorSummary();
      if (errorSummary.totalErrors > 0) {
        summary += `Error Summary:\n`;
        summary += `  - Total Errors: ${errorSummary.totalErrors}\n`;
        summary += `  - Validation Errors: ${errorSummary.validationErrors}\n`;
        summary += `  - API Errors: ${errorSummary.apiErrors}\n`;
        summary += `  - General Errors: ${errorSummary.generalErrors}\n`;
        summary += `\n`;

        // Include sample errors
        if (errorSummary.errorDetails.validation.length > 0) {
          summary += `Sample Validation Errors:\n`;
          errorSummary.errorDetails.validation.forEach((error, index) => {
            summary += `  ${index + 1}. [${error.timestamp}] ${error.message}\n`;
            if (error.errors && Array.isArray(error.errors)) {
              error.errors.forEach(err => summary += `     - ${err}\n`);
            }
          });
          summary += `\n`;
        }

        if (errorSummary.errorDetails.api.length > 0) {
          summary += `Sample API Errors:\n`;
          errorSummary.errorDetails.api.forEach((error, index) => {
            summary += `  ${index + 1}. [${error.timestamp}] ${error.message}\n`;
            if (error.error) summary += `     - ${error.error}\n`;
          });
          summary += `\n`;
        }
      }

      summary += `================================================================================\n`;
      summary += `                              END OF LOG\n`;
      summary += `================================================================================\n`;

      this.appendToFile(summary);

      logger.info(`[Performance] Performance log saved to: ${this.performanceFile}`);
    } catch (error) {
      logger.error(`[Performance] Failed to write final summary: ${error.message}`);
    }
  }

  /**
   * Generate summary statistics from all steps
   */
  generateSummary() {
    const steps = this.metrics.steps;
    if (steps.length === 0) return {};

    const durations = steps.map(s => s.durationMs);
    const memoryDeltas = steps.map(s => s.memoryDelta.rssChange);

    return {
      totalSteps: steps.length,
      averageStepDuration: Math.round((durations.reduce((a, b) => a + b, 0) / durations.length) * 100) / 100,
      longestStep: {
        name: steps.find(s => s.durationMs === Math.max(...durations))?.stepName,
        duration: Math.max(...durations)
      },
      shortestStep: {
        name: steps.find(s => s.durationMs === Math.min(...durations))?.stepName,
        duration: Math.min(...durations)
      },
      totalMemoryChange: memoryDeltas.reduce((a, b) => a + b, 0),
      stepBreakdown: steps.map(s => ({
        name: s.stepName,
        duration: s.durationMs,
        percentage: Math.round((s.durationMs / this.metrics.totalDurationMs) * 10000) / 100
      }))
    };
  }

  /**
   * Format memory usage for logging
   * @param {Object} memoryUsage - Memory usage object from process.memoryUsage()
   */
  formatMemoryUsage(memoryUsage) {
    return {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100} MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100} MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100} MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100} MB`
    };
  }

  /**
   * Calculate memory delta between two memory usage objects
   * @param {Object} before - Memory usage before
   * @param {Object} after - Memory usage after
   */
  calculateMemoryDelta(before, after) {
    return {
      rssChange: Math.round((after.rss - before.rss) / 1024 / 1024 * 100) / 100,
      heapTotalChange: Math.round((after.heapTotal - before.heapTotal) / 1024 / 1024 * 100) / 100,
      heapUsedChange: Math.round((after.heapUsed - before.heapUsed) / 1024 / 1024 * 100) / 100,
      externalChange: Math.round((after.external - before.external) / 1024 / 1024 * 100) / 100
    };
  }

  /**
   * Format duration in human-readable format
   * @param {number} milliseconds - Duration in milliseconds
   */
  formatDuration(milliseconds) {
    if (milliseconds < 1000) {
      return `${Math.round(milliseconds * 100) / 100}ms`;
    } else if (milliseconds < 60000) {
      return `${Math.round(milliseconds / 1000 * 100) / 100}s`;
    } else {
      const minutes = Math.floor(milliseconds / 60000);
      const seconds = Math.round((milliseconds % 60000) / 1000 * 100) / 100;
      return `${minutes}m ${seconds}s`;
    }
  }

  /**
   * Get current metrics (useful for intermediate reporting)
   */
  getCurrentMetrics() {
    return { ...this.metrics };
  }

  /**
   * Log intermediate progress
   * @param {string} message - Progress message
   * @param {Object} data - Additional data to log
   */
  logProgress(message, data = {}) {
    // Check if this is an error and categorize it
    if (data.error || data.errors) {
      this.trackError(message, data);
    }

    // Write progress to file
    this.writeProgressUpdate(message, data);

    logger.info(`[Performance] Progress: ${message}`, {
      sessionId: this.sessionId
    });
  }

  /**
   * Track different types of errors with full stack traces
   * @param {string} message - Error message
   * @param {Object} data - Error data
   */
  trackError(message, data) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      message,
      ...data
    };

    // Add stack trace if error object is provided
    if (data.error && typeof data.error === 'object') {
      if (data.error.stack) {
        errorEntry.stackTrace = data.error.stack;
      }
      if (data.error.message) {
        errorEntry.errorMessage = data.error.message;
      }
    }

    // Add detailed error information to the log entry
    let detailedLogEntry = `[${errorEntry.timestamp}] ERROR: ${message}\n`;

    if (errorEntry.messageId) {
      detailedLogEntry += `  📧 Message ID: ${errorEntry.messageId}\n`;
    }

    if (errorEntry.agentName) {
      detailedLogEntry += `  🤖 Agent: ${errorEntry.agentName}\n`;
    }

    if (errorEntry.errors && Array.isArray(errorEntry.errors)) {
      detailedLogEntry += `  ❌ Validation Errors:\n`;
      errorEntry.errors.forEach((err, index) => {
        detailedLogEntry += `     ${index + 1}. ${err}\n`;
      });
    }

    if (errorEntry.errorMessage) {
      detailedLogEntry += `  💥 Error Message: ${errorEntry.errorMessage}\n`;
    }

    if (errorEntry.stackTrace) {
      detailedLogEntry += `  📚 Stack Trace:\n`;
      errorEntry.stackTrace.split('\n').forEach(line => {
        detailedLogEntry += `     ${line}\n`;
      });
    }

    detailedLogEntry += `\n`;

    // Write detailed error to log file immediately
    this.appendToFile(detailedLogEntry);

    // Categorize errors
    if (message.includes('validation') || message.includes('Validation')) {
      this.metrics.validationErrors.push(errorEntry);
    } else if (message.includes('API') || message.includes('api')) {
      this.metrics.apiErrors.push(errorEntry);
    } else {
      this.metrics.errors.push(errorEntry);
    }
  }

  /**
   * Get error summary
   */
  getErrorSummary() {
    return {
      totalErrors: this.metrics.errors.length + this.metrics.validationErrors.length + this.metrics.apiErrors.length,
      validationErrors: this.metrics.validationErrors.length,
      apiErrors: this.metrics.apiErrors.length,
      generalErrors: this.metrics.errors.length,
      errorDetails: {
        validation: this.metrics.validationErrors.slice(0, 5), // First 5 validation errors
        api: this.metrics.apiErrors.slice(0, 5), // First 5 API errors
        general: this.metrics.errors.slice(0, 5) // First 5 general errors
      }
    };
  }
}

module.exports = PerformanceMonitor;
