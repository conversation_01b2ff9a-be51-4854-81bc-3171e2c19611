// middleware/uploadToBase64.js
const multer = require("multer");
const path = require("path");

const upload = multer({ storage: multer.memoryStorage() });

function uploadToBase64(fieldName) {
  const handler = upload.single(fieldName);

  return (req, res, next) => {
    handler(req, res, (err) => {
      if (err) return next(err);

      if (req.file) {
        const mimeType = req.file.mimetype;
        const base64 = req.file.buffer.toString("base64");
        req.body[fieldName] = `data:${mimeType};base64,${base64}`;
      }

      next();
    });
  };
}

module.exports = uploadToBase64;
