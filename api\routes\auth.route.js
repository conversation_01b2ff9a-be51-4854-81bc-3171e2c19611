const express = require('express');
const validate = require('../middlewares/validate');
const authValidation = require('../validations/auth.validation');
const authController = require('../controllers/auth.controller');
const passport = require('../config/passport'); 

/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: Authentication
 */
const router = express.Router();

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Login
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *             example:
 *               email: '<EMAIL>'
 *               password: 'Pa$$$$w0rd!'
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 identity:
 *                   $ref: '#/components/schemas/Identity'
 *                 tokens:
 *                   $ref: '#/components/schemas/AuthTokens'
 *       "401":
 *         description: Invalid email or password
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               code: 401
 *               message: Invalid email or password
 */
router.post('/login', validate(authValidation.login), authController.login);

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: Logout
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *             example:
 *               refreshToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1ZWJhYzUzNDk1NGI1NDEzOTgwNmMxMTIiLCJpYXQiOjE1ODkyOTg0ODQsImV4cCI6MTU4OTMwMDI4NH0.m1U63blB0MLej_WfB7yC2FTMnCziif9X8yzwDEfJXAg
 *     responses:
 *       "204":
 *         description: No content
 *       "404":
 *         $ref: '#/components/responses/NotFound'
 */
router.post('/logout', validate(authValidation.logout), authController.logout);

/**
 * @swagger
 * /auth/refresh-tokens:
 *   post:
 *     summary: Refresh auth tokens
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *             example:
 *               refreshToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1ZWJhYzUzNDk1NGI1NDEzOTgwNmMxMTIiLCJpYXQiOjE1ODkyOTg0ODQsImV4cCI6MTU4OTMwMDI4NH0.m1U63blB0MLej_WfB7yC2FTMnCziif9X8yzwDEfJXAg
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthTokens'
 *       "401":
 *         $ref: '#/components/responses/Unauthorized'
 */
module.exports = router;
router.post('/refresh-tokens', validate(authValidation.refreshTokens), authController.refreshTokens);
/* ---------------------
   SSO Endpoints
--------------------- */

/**
 * @swagger
 * /auth/saml/login:
 *   get:
 *     summary: Initiate SAML login
 *     tags: [Auth]
 *     responses:
 *       "302":
 *         description: Redirects to the SAML Identity Provider.
 */
router.get('/saml/login', passport.authenticate('saml', { session: false }));
/**
 * @swagger
 * /auth/saml/callback:
 *   post:
 *     summary: SAML callback endpoint
 *     tags: [Auth]
 *     description: Endpoint for the SAML Identity Provider to POST the authentication response.
 *     responses:
 *       "200":
 *         description: Login successful via SAML.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 identity:
 *                   $ref: '#/components/schemas/Identity'
 *                 tokens:
 *                   $ref: '#/components/schemas/AuthTokens'
 *       "400":
 *         description: Invalid provider error.
 */
router.post('/saml/callback', passport.authenticate('saml', { session: false }), authController.handleSsoCallback);

/**
 * @swagger
 * /auth/oidc/login:
 *   get:
 *     summary: Initiate OpenID Connect login
 *     tags: [Auth]
 *     responses:
 *       "302":
 *         description: Redirects to the OpenID Connect provider.
 */
router.get('/oidc/login', passport.authenticate('oidc', { session: false }));
/**
 * @swagger
 * /auth/oidc/callback:
 *   get:
 *     summary: OpenID Connect callback endpoint
 *     tags: [Auth]
 *     responses:
 *       "200":
 *         description: Login successful via OpenID Connect.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 identity:
 *                   $ref: '#/components/schemas/Identity'
 *                 tokens:
 *                   $ref: '#/components/schemas/AuthTokens'
 *       "400":
 *         description: Invalid provider error.
 */
router.get('/oidc/callback', passport.authenticate('oidc', { session: false }), authController.handleSsoCallback);

/**
 * @swagger
 * /auth/azure/login:
 *   get:
 *     summary: Initiate Azure AD login
 *     tags: [Auth]
 *     responses:
 *       "302":
 *         description: Redirects to the Azure AD login page.
 */
router.get('/azure/login', passport.authenticate('azure', { session: false }));
/**
 * @swagger
 * /auth/azure/callback:
 *   get:
 *     summary: Azure AD callback endpoint
 *     tags: [Auth]
 *     responses:
 *       "200":
 *         description: Login successful via Azure AD.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 identity:
 *                   $ref: '#/components/schemas/Identity'
 *                 tokens:
 *                   $ref: '#/components/schemas/AuthTokens'
 *       "400":
 *         description: Invalid provider error.
 */
router.get('/azure/callback', passport.authenticate('azure', { session: false }), authController.handleSsoCallback);
