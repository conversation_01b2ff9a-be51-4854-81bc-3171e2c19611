module.exports = (sequelize, DataTypes) => {
  const PatientAppointmentGuestView = sequelize.define(
    "PatientAppointmentGuestView",
    {
      patient_guest_id: {
        type: DataTypes.UUID,
        primaryKey: true,
      },
      appointment_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      patient_id: {
        type: DataTypes.UUID,
      },
      appointment_guest_id: {
        type: DataTypes.UUID,
      },
      first_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      last_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      phone: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      friends_and_family: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      is_walkin: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      guest_image: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      guest_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      mrn: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      guest_arrival_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      guest_departure_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      guest_pin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      appointment_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      guest_full_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      patient_full_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      screening: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      facility_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      provider_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      floor_number: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      building_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      room_number: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      appointment_guest_status: {
        type: DataTypes.INTEGER,
      },
      appointment_guest_status_name: {
        type: DataTypes.STRING,
      },
      computed_status_name: {
        type: DataTypes.VIRTUAL,
        get() {
          return (
            this.getDataValue("appointment_guest_status_name") || "Registered"
          );
        },
      },
    },
    {
      tableName: "view_patient_appointment_guest",
      timestamps: false,
      freezeTableName: true,
    }
  );

  return PatientAppointmentGuestView;
};
