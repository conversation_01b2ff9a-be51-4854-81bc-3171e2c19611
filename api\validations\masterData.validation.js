const Joi = require("joi");
const { exists } = require("./custom.validation");

const getMasterData = {
  query: Joi.object().keys({
    groups: Joi.alternatives()
      .try(
        Joi.array().items(Joi.string().required()).min(1),
        Joi.string().required()
      )
      .required()
      .description(
        "Array of group IDs to filter master data records. If a single group is provided, it will be wrapped in an array."
      ),
  }),
};

const media = {
  params: Joi.object().keys({
    model: Joi.string().required().description("Model name, e.g. 'Facility'"),
  }),
  query: Joi.object().keys({
    key: Joi.string().optional().default("image").description("Media key, defaults to 'image'"),
    thumbnail_only: Joi.boolean().optional().default(false).description("Whether to return thumbnail version"),
    value: Joi.string().required().description("Media reference UUID").external((value, helpers) => {
      const { model } = helpers.state.ancestors[1].params;
      const { key } = helpers.state.ancestors[1].query;
      return exists(model, key)(value, helpers);
    }),
  }),
};

const identityHub = {
  query: Joi.object().keys({
    name: Joi.string().optional(),
    eid: Joi.string().optional(),
    search: Joi.string().optional(),
  }),
};

const getAllAccessLevel = {
  query: Joi.object().keys({
    default_access_guest: Joi.boolean().optional().default(true).description("Filter for FacilityAccessLevel.default_access_guest (default: true)"),
    requestable_guest: Joi.boolean().optional().default(true).description("Filter for FacilityAccessLevel.requestable_guest (default: true)")
  })
};

module.exports = {
  getMasterData,
  media,
  identityHub,
  getAllAccessLevel,
};
