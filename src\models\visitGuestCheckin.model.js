// filepath: e:\Office\api\models\visitGuestCheckin.model.js
const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const VisitGuestCheckin = sequelize.define(
    "VisitGuestCheckin",
    {
      visit_guest_checkin_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      guest_visit_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "guest_visit",
          key: "guest_visit_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to guest visit",
      },
      check_in_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "Check-in time for the guest visit",
      },
      check_out_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "Check-out time for the guest visit",
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "visit_guest_checkin",
      timestamps: true,
      underscored: true,
    }
  );

  VisitGuestCheckin.associate = (models) => {
    VisitGuestCheckin.belongsTo(models.GuestVisit, {
      foreignKey: "guest_visit_id",
      as: "guestVisit",
    });
  };

  history(VisitGuestCheckin, sequelize, DataTypes);

  return VisitGuestCheckin;
};