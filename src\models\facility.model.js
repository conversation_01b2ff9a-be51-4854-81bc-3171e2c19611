const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const Facility = sequelize.define(
    "Facility",
    {
      facility_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      image: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: false,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      facility_code: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: true,
      },
      facility_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      timezone_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "timezone",
          key: "timezone_id",
        },
        onDelete: "SET NULL",
      },
      phone: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      geo_location_code: {
        type: DataTypes.DECIMAL(10, 7),
        allowNull: true,
      },
      other_code: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      facility_url: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      connected_applications: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "facility",
      timestamps: true,
      underscored: true,
    }
  );

  Facility.associate = (models) => {
    Facility.hasOne(models.Address, {
      foreignKey: "facility_id",
      onDelete: "CASCADE",
      as: "address",
    });

    Facility.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "facility_status_name",
      constraints: false,
      scope: {
        group: "facility_status",
      },
    });

    Facility.belongsTo(models.MasterData, {
      foreignKey: "facility_type",
      targetKey: "key",
      as: "facility_type_name",
      constraints: false,
      scope: {
        group: "facility_type",
      },
    });
  };

  history(Facility, sequelize, DataTypes);

  media(Facility, sequelize, DataTypes);

  return Facility;
};
