const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const Guest = sequelize.define(
    "Guest",
    {
      guest_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      first_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "Guest's first name",
      },
      last_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "Guest's last name",
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          isEmail: true,
        },
        comment: "Guest's email address",
      },
      image: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: false,
      },
      mobile_phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: "Guest's mobile phone number",
      },
      date_of_birth: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      company: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "Guest's company name",
      },
      private_visitor: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether the guest is a private visitor",
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "guest",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["email"],
          unique: true,
        },
        {
          fields: ["first_name"],
        },
        {
          fields: ["last_name"],
        },
        {
          fields: ["company"],
        },
        {
          fields: ["private_visitor"],
        },
      ],
    }
  );

  Guest.associate = (models) => {
    // Many-to-many relationship with Visit through GuestVisit
    Guest.belongsToMany(models.Visit, {
      through: models.GuestVisit,
      foreignKey: "guest_id",
      otherKey: "visit_id",
      as: "visits",
    });

    // Direct association with GuestVisit for easier querying
    Guest.hasMany(models.GuestVisit, {
      foreignKey: "guest_id",
      as: "guestVisits",
    });
  };

  // Apply plugins
  media(Guest, sequelize, DataTypes);
  history(Guest, sequelize, DataTypes);

  return Guest;
};
