module.exports = {
  apps: [{
    name: 'hl7_parser',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=hl7_queue',
    env: {
      NODE_ENV: 'development'
    },
  },
  {
    name: 'hl7store',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=hl7_store',
    env: {
      NODE_ENV: 'development'
    },
  },
  {
    name: 'patient_admission_procesor',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=patient_admission_procesor',
    env: {
      NODE_ENV: 'development'
    }
  },
  {
    name: 'notification_procesor',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=notification_queue',
    env: {
      NODE_ENV: 'development'
    }
  },
  {
    name: 'email_procesor',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=email_queue',
    env: {
      NODE_ENV: 'development'
    }
  },
  {
    name: 'text_procesor',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=text_queue',
    env: {
      NODE_ENV: 'development'
    }
  },
  {
    name: 'hr_data_processor',
    script: './index.js',
    cwd: '/opt/processor',
    args: '--queue=hr_csv_data',
    env: {
      NODE_ENV: 'development'
    }
  },
  ]
};
