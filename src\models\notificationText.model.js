const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const NotificationText = sequelize.define(
    "NotificationText",
    {
      notification_text_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      template: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      receiver_column: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      language: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "notification_text",
      timestamps: true,
      underscored: true,
    }
  );

  history(NotificationText, sequelize, DataTypes);

  return NotificationText;
};