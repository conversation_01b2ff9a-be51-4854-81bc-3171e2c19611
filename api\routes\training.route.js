const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const TrainingValidation = require("../validations/training.validation");
const TrainingController = require("../controllers/training.controller");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Training:
 *       type: object
 *       required:
 *         - name
 *         - status
 *         - identity_id
 *       properties:
 *         training_id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the training
 *         name:
 *           type: string
 *           description: Training/Course name
 *         course_number:
 *           type: string
 *           description: Course identification number
 *         category:
 *           type: string
 *           description: Training category
 *         course_type:
 *           type: string
 *           description: Type of course
 *         recurrence:
 *           type: string
 *           description: How often training needs to be repeated
 *         due_date:
 *           type: string
 *           format: date
 *           description: Date when training is due
 *         date_completed:
 *           type: string
 *           format: date
 *           description: Date when training was completed
 *         score:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           description: Training score
 *         status:
 *           type: string
 *           enum: [Pass, Fail]
 *           description: Training status
 *         identity_id:
 *           type: string
 *           format: uuid
 *           description: Reference to identity
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /training:
 *   post:
 *     summary: Create a new training record
 *     tags: [Training]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - status
 *               - identity_id
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Fire Safety Training"
 *               course_number:
 *                 type: string
 *                 example: "FS-101"
 *               category:
 *                 type: string
 *                 example: "Safety"
 *               course_type:
 *                 type: integer
 *                 example: "0"
 *               recurrence:
 *                 type: integer
 *                 example: "0"
 *               due_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-31"
 *               date_completed:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               score:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *                 example: 85.5
 *               status:
 *                 type: integer
 *                 example: "0"
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       201:
 *         description: Training record created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/",
  auth("create_training"),
  validate(TrainingValidation.createTraining),
  TrainingController.createTraining
);

/**
 * @swagger
 * /training:
 *   get:
 *     summary: Get all training records with pagination and filtering
 *     tags: [Training]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: identity_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by identity ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Pass, Fail]
 *         description: Filter by status
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *     responses:
 *       200:
 *         description: Training records retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/",
  auth("view_training"),
  validate(TrainingValidation.getTrainings),
  TrainingController.getTrainings
);

/**
 * @swagger
 * /training/{training_id}:
 *   get:
 *     summary: Get a training record by ID
 *     tags: [Training]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: training_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Training ID
 *     responses:
 *       200:
 *         description: Training record retrieved successfully
 *       404:
 *         description: Training record not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/:training_id",
  auth("view_training"),
  validate(TrainingValidation.getTrainingById),
  TrainingController.getTrainingById
);

/**
 * @swagger
 * /training/{training_id}:
 *   put:
 *     summary: Update a training record by ID
 *     tags: [Training]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: training_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Training ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               course_number:
 *                 type: string
 *               category:
 *                 type: string
 *               course_type:
 *                 type: integer
 *               recurrence:
 *                 type: integer
 *               due_date:
 *                 type: string
 *                 format: date
 *               date_completed:
 *                 type: string
 *                 format: date
 *               score:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               status:
 *                 type: string
 *                 enum: [Pass, Fail]
 *     responses:
 *       200:
 *         description: Training record updated successfully
 *       404:
 *         description: Training record not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/:training_id",
  auth("edit_training"),
  validate(TrainingValidation.updateTraining),
  TrainingController.updateTraining
);

/**
 * @swagger
 * /training/{training_id}:
 *   delete:
 *     summary: Delete a training record by ID
 *     tags: [Training]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: training_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Training ID
 *     responses:
 *       200:
 *         description: Training record deleted successfully
 *       404:
 *         description: Training record not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/:training_id",
  auth("delete_training"),
  validate(TrainingValidation.deleteTraining),
  TrainingController.deleteTraining
);

module.exports = router;
