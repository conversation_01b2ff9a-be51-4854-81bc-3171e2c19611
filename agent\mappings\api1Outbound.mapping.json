{"mappingType": "apiTransform", "apiConfig": {"method": "POST", "headers": {"Content-Type": "application/json", "X-API-Key": "{{api_key}}", "X-Client-ID": "{{client_id}}"}, "timeout": 45000, "retries": 3, "retryDelay": 2000, "retryBackoff": "exponential"}, "dataTransform": {"type": "object", "properties": {"employee": {"employeeId": "{{Identity.eid}}", "personalInfo": {"email": "{{Identity.email}}", "fullName": "{{Identity.first_name}} {{Identity.middle_name}} {{Identity.last_name}}", "firstName": "{{Identity.first_name}}", "lastName": "{{Identity.last_name}}", "nationalIdentifier": "{{Identity.national_id}}", "phoneNumber": "{{Identity.mobile}}"}, "employment": {"startDate": "{{Identity.start_date}}", "endDate": "{{Identity.end_date}}", "status": "{{Identity.status}}", "companyName": "{{Identity.company}}", "organizationUnit": "{{Identity.organization}}", "companyCode": "{{Identity.company_code}}", "jobTitle": "{{Identity.job_title}}", "jobCode": "{{Identity.job_code}}"}}, "batchInfo": {"batchId": "{{batch_id}}", "timestamp": "{{current_timestamp}}", "source": "caremate", "totalRecords": "{{batch_size}}"}}}, "validation": {"required": ["employee.employeeId", "employee.personalInfo.email", "employee.personalInfo.firstName", "employee.personalInfo.lastName"], "rules": {"employee.personalInfo.email": {"type": "email", "required": true}, "employee.personalInfo.firstName": {"type": "string", "required": true, "minLength": 1}, "employee.personalInfo.lastName": {"type": "string", "required": true, "minLength": 1}, "employee.employeeId": {"type": "string", "required": true}}}, "responseMapping": {"success": {"statusCodes": [200, 201], "responseFields": {"batchId": "response.data.batchId", "processedCount": "response.data.processedCount", "status": "response.status", "message": "response.message"}}, "error": {"statusCodes": [400, 401, 403, 404, 422, 500], "errorFields": {"errorCode": "response.error.code", "errorMessage": "response.error.message", "failedRecords": "response.error.failedRecords"}}}}