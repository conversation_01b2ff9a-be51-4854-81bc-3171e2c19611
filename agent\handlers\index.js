const fs = require("fs");
const path = require("path");

const handlers = {};

// Get the current directory where the handler files reside.
const handlersDirectory = __dirname;

// Read all files in the handlers directory that end with ".handler.js", excluding this index file.
fs.readdirSync(handlersDirectory)
  .filter((file) => file.endsWith(".handler.js") && file !== "index.js")
  .forEach((file) => {
    // Use titleCase (or any naming convention) to set the handler name.
    const handlerName = path.basename(file, ".handler.js");
    const handlerModule = require(path.join(handlersDirectory, file));
    handlers[handlerName] = handlerModule;
  });
  
// console.log(handlers)

module.exports = handlers;
