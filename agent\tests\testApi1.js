const express = require('express');
const fs = require('fs');
const path = require('path');
const { createLogger, format, transports } = require('winston');
require('winston-daily-rotate-file');
const expressWinston = require('express-winston');

const app = express();
const PORT = 3051;

// Ensure logs directory exists
const logDirectory = path.join(__dirname, 'logs1');
if (!fs.existsSync(logDirectory)) {
  fs.mkdirSync(logDirectory);
}

// Winston logger setup
const dailyRotateTransport = new transports.DailyRotateFile({
  filename: path.join(logDirectory, 'app-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d'
});

const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.printf(({ timestamp, level, message, ...meta }) => {
      const metaString = Object.keys(meta).length
        ? ` ${JSON.stringify(meta)}`
        : '';
      return `${timestamp} [${level}]: ${message}${metaString}`;
    })
  ),
  transports: [
    new transports.Console(),
    dailyRotateTransport
  ],
  exitOnError: false
});

// Middleware to parse JSON and URL-encoded bodies
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// HTTP request logging
app.use(expressWinston.logger({
  winstonInstance: logger,
  meta: true,
  msg: '{{req.method}} {{req.url}} {{res.statusCode}} - {{res.responseTime}}ms',
  expressFormat: false,
  colorize: false,
}));

// POST /log endpoint
app.post('/log', (req, res) => {
  const data = req.body;
  if (!data || Object.keys(data).length === 0) {
    logger.warn('POST /log called with empty body');
    return res.status(400).json({ error: 'No data provided' });
  }

  // Log payload at info level
  logger.info('Received data on /log', { payload: data });
  res.status(200).json({ message: 'Data logged successfully' });
});

// Error-logging middleware
app.use(expressWinston.errorLogger({
  winstonInstance: logger
}));

// Start the server
app.listen(PORT, () => {
  logger.info(`Server listening on http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('Shutting down server...');
  process.exit(0);
});
