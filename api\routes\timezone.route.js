const express = require("express");
const validate = require("../middlewares/validate");
const TimezoneValidation = require("../validations/timezone.validation");
const TimezoneController = require("../controllers/timezone.controller");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Timezones
 *   description: Timezone management
 */

router
  .route("/")
  /**
   * @swagger
   * /timezones:
   *   get:
   *     summary: Get all timezones
   *     tags: [Timezones]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of timezones
   */
  .get(auth("view_timezones"), TimezoneController.index)
  /**
   * @swagger
   * /timezones:
   *   post:
   *     summary: Create a new timezone
   *     tags: [Timezones]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               country_id:
   *                 type: string
   *                 description: ID of the country
   *                 example: 123e4567-e89b-12d3-a456-426614174000
   *               code:
   *                 type: string
   *                 description: Timezone code
   *                 example: PST
   *     responses:
   *       201:
   *         description: Timezone created
   */
  .post(auth("create_timezone"), validate(TimezoneValidation.create), TimezoneController.create);

router
  .route("/:timeId")
  /**
   * @swagger
   * /timezones/{timeId}:
   *   get:
   *     summary: Get a timezone by ID
   *     tags: [Timezones]
   *     parameters:
   *       - name: timeId
   *         in: path
   *         required: true
   *     responses:
   *       200:
   *         description: Timezone details
   */
  .get(auth("view_timezone"), validate(TimezoneValidation.timezone), TimezoneController.show)
  /**
   * @swagger
   * /timezones/{timeId}:
   *   patch:
   *     summary: Update a timezone
   *     tags: [Timezones]
   *     parameters:
   *       - name: timeId
   *         in: path
   *         required: true
   *     responses:
   *       200:
   *         description: Timezone updated
   */
  .patch(auth("edit_timezone"), validate(TimezoneValidation.update), TimezoneController.update)
  /**
   * @swagger
   * /timezones/{timeId}:
   *   delete:
   *     summary: Delete a timezone
   *     tags: [Timezones]
   *     parameters:
   *       - name: timeId
   *         in: path
   *         required: true
   *     responses:
   *       204:
   *         description: Timezone deleted
   */
  .delete(auth("delete_timezone"), validate(TimezoneValidation.timezone), TimezoneController.delete);

module.exports = router;