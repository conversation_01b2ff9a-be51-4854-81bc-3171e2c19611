const Joi = require("joi");
const hl7Mapping = require("../mappings/hl7Message.mapping.json");
const { Patient, PatientIdentifier, Appointment } = require("../models");

// Map model names to Sequelize classes:
const modelMap = { Patient, PatientIdentifier, Appointment };

// Helper to decide if a field should be required
function isFieldRequired(modelName, fieldName) {
  const model = modelMap[modelName];
  if (!model) return false;
  const attr = model.rawAttributes[fieldName];
  // required if allowNull === false and no defaultValue specified
  return attr && attr.allowNull === false && attr.defaultValue === undefined;
}

// Build Joi schema
const schemaDesc = {};
for (const [segField, targetPath] of Object.entries(hl7Mapping)) {
  // base rule: string
  let rule = Joi.string();
  // date fields get a strict pattern (accept YYYYMMDD or YYYYMMDDhhmmss)
  if (targetPath.endsWith("_date")) {
      rule = rule.pattern(/^\d{8}(\d{6})?$/).message(`${segField} must be YYYYMMDD or YYYYMMDDhhmmss`);
  }
  // if mapped to a required target, make it required
  const [modelName, fieldName] = targetPath.split(".");
  if (isFieldRequired(modelName, fieldName)) {
    rule = rule.required();
  } else {
    rule = rule.optional();
  }
  schemaDesc[segField] = rule;
}

// Allow unknown keys
const eventSchema = Joi.object(schemaDesc);

module.exports = {
  hl7Event: {
    body: eventSchema,
  },
};
