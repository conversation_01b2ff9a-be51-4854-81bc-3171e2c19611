stages:
  - build
  # - push

services:
  - docker:dind

variables:
  DOCKER_TLS_CERTDIR: ""
  REGISTRY_URL: "$DOCKER_REGISTRY"
  IMAGE_NAME: "care/api"

before_script:
  - docker login -u "$DOCKER_REGISTRY_USER" -p "$DOCKER_REGISTRY_PASSWORD" "$DOCKER_REGISTRY"

build:
  stage: build
  tags:
    - high-perf
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop" || $CI_MERGE_REQUEST_IID || $CI_COMMIT_TAG'
  image: docker:latest
  script:
    - |
      export VERSION=$(echo "$CI_COMMIT_TAG" | sed 's/^v//')
      export IMAGE_TAGS="$VERSION $CI_COMMIT_REF_NAME"
      echo "IMAGE_TAGS: $IMAGE_TAGS"
      docker build --build-arg CI_JOB_TOKEN=$CI_JOB_TOKEN -t $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA .
      set -e
      for tag in $IMAGE_TAGS; do
        docker tag $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA $REGISTRY_URL/$IMAGE_NAME:$tag
        docker push $REGISTRY_URL/$IMAGE_NAME:$tag || exit 1
      done
      

# push:
#   stage: push
#   image: docker:latest
#   rules:
#     - if: '$CI_COMMIT_BRANCH == "development" || $CI_MERGE_REQUEST_IID || $CI_COMMIT_TAG' 
#   script:
#     - |
#       set -e
#       export VERSION=$(echo "$CI_COMMIT_TAG" | sed 's/^v//')
#       export IMAGE_TAGS="$VERSION $CI_COMMIT_REF_NAME"
#       echo "IMAGE_TAGS: $IMAGE_TAGS"
      
