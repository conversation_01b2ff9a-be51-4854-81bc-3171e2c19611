const { tokenTypes } = require("../config/attributes");

module.exports = (sequelize, DataTypes) => {
  const Token = sequelize.define(
    "Token",
    {
      token: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
      },
      type: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isIn: {
            // Only allow the refresh token type (you can add more types if needed)
            args: [[tokenTypes.REFRESH]],
            msg: `Type must be one of: ${tokenTypes.REFRESH}`,
          },
        },
      },
      expires: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      blacklisted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: "token",
      timestamps: true,
    }
  );

  // Set up associations (assuming your index file will call associate on each model)
  Token.associate = (models) => {
    // Assuming that the foreign key field will be named 'identity' (as in your model)
    Token.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      onDelete: "CASCADE",
    });
  };

  return Token;
};
