const Joi = require("joi");

const getDeviceSetting = {
  params: Joi.object().keys({
    device_id: Joi.string().uuid().required(),
  }),
};

const fetchGuestByPin = {
  body: Joi.object().keys({
    device_id: Joi.string().uuid().required(),
    guest_pin: Joi.string().required(),
    guest_name: Joi.string().required(),
  }),
};

const getDeviceTemplate = {
  // No validation needed for GET all endpoint
};

const getPatientByAppointmentAndPhone = {
  body: Joi.object().keys({
    device_id: Joi.string().uuid().required(),
    appointment_guest_id: Joi.string().uuid().required(),
    phone_last_4: Joi.string().length(4).pattern(/^\d{4}$/).required(),
  }),
};

const getOutpatientDetails = {
  body: Joi.object().keys({
    device_id: Joi.string().uuid().required(),
    cellphone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{0,20}$/).required(),
    birth_date: Joi.date().iso().required(),
  }),
};

const performPatientGuestCheckin = {
  body: Joi.object().keys({
    device_id: Joi.string().uuid().required(),
    appointment_id: Joi.string().uuid().required(),
    appointment_guest_id: Joi.string().uuid().required(),
  }),
};

const getInpatientAppointmentDetails = {
  body: Joi.object().keys({
    device_id: Joi.string().uuid().required(),
    facility_id: Joi.string().uuid().required(),
    phone_last_4: Joi.string().length(4).pattern(/^\d{4}$/).required(),
    first_name_first_3: Joi.string().min(3).max(3).pattern(/^[a-zA-Z]{3}$/).required(),
    last_name_first_3: Joi.string().min(3).max(3).pattern(/^[a-zA-Z]{3}$/).required(),
  }),
};

module.exports = {
  getDeviceSetting,
  getDeviceTemplate,
  fetchGuestByPin,
  getPatientByAppointmentAndPhone,
  getOutpatientDetails,
  performPatientGuestCheckin,
  getInpatientAppointmentDetails,
};
